{"devDependencies": {"@hotwired/stimulus": "^3.0.0", "@playwright/test": "^1.49", "@symfony/stimulus-bridge": "^3.2.0", "@symfony/ux-chartjs": "file:vendor/symfony/ux-chartjs/assets", "@symfony/webpack-encore": "^5.0.1", "@types/node": "^20.9.1", "chart.js": "^3.4.1 || ^4.0", "core-js": "^3.0.0", "regenerator-runtime": "^0.13.2", "stimulus": "^2.0.0", "webpack-notifier": "^1.6.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"html2canvas": "^1.0.0-rc.7", "jquery": "^3.5.1", "jquery.quicksearch": "^2.4.0", "lodash": "^4.17.20", "multiselect": "^0.9.12"}}