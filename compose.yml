services:
  web:
    image: gitlab.alienor.net:5050/dev-projets-alienor/eduprat:dev83-sf64
    build: .docker/anet_web_eduprat_83
    depends_on:
      - mariadb
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - '.:/var/www/html/'
    links:
      - mariadb
#      - elasticsearch
      - gotenberg
  mariadb:
    image: 'mariadb:10.11.3'
    volumes:
      - './.data_mysql:/var/lib/mysql'
    command:
      - '--default-authentication-plugin=mysql_native_password'
      - '--lower_case_table_names=1'
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: anetdb
      MYSQL_USER: userdb
      MYSQL_PASSWORD: passdb
    ports:
      - "3306:3306"
  phpmyadmin:
    image: 'phpmyadmin/phpmyadmin:latest'
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: userdb
      PMA_PASSWORD: passdb
    ports:
      - '8083:80'
    links:
      - mariadb
#  elasticsearch:
#    image: 'elasticsearch:2.3.0'
#    environment:
#      - cluster.name=docker-cluster
#      - bootstrap.memory_lock=true
#      - ES_JAVA_OPTS=-Xms512m -Xmx512m
#      - discovery.type=single-node
#      - http.cors.enabled=true
#      - http.cors.allow-credentials=true
#      - 'http.cors.allow-headers=X-Requested-With,X-Auth-Token,Content-Type,Content-Length,Authorization'
#      - 'http.cors.allow-origin=/https?:\/\/localhost(:[0-9]+)?/'
#    volumes:
#      - './.data_elastic:/usr/share/elasticsearch/data2.3.0'
#    ulimits:
#      memlock:
#        soft: -1
#        hard: -1
#    ports:
#      - '9300:9200'

  mailcatcher:
    image: dockage/mailcatcher:0.9
    ports:
      - "1080:1080"  # Port for web interface
      - "1025:1025"  # SMTP port

  gotenberg:
    image: gotenberg/gotenberg:8
    command: gotenberg --chromium-allow-file-access-from-files
    ports:
      - "3000:3000"
