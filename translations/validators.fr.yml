formation:
  imageFile_maxSize: La taille maximale du fichier est de 1Mo.
  mimeTypes: Le type du fichier est invalide ({{ type }}). Les types autorisés sont PDF et JPEG
  mimeTypesAttestation: Le type du fichier est invalide. Seul les fichiers PDF, JPEG et PNG sont autorisés.
  mimeTypesIconographie: Le type du fichier est invalide. Seul les fichiers JPEG et PNG sont autorisés.
  mimeTypesPDF: Le type du fichier est invalide. Seuls les fichiers PDF sont autorisés.

participationActionSheet:
  mimeTypes: Le type du fichier est invalide. Seul les fichiers Excel sont autorisés

user.form.password_mismatch: Les deux mots de passe ne correspondent pas
password.edit.mismatch: Les deux mots de passe ne correspondent pas
password.edit.constraints: Le mot de passe doit être composé d'au minimum 8 caractères et doit contenir au moins un caractère en minuscule, un en majuscule, un chiffre et un caractère spécial

global.zipCode: "Veuillez respecter le format d'un code postal : 5 chiffres (exemple : 33110)"
global.phone: "Veuillez respecter le format d'un numéro de téléphone : 0 + 9 chiffres (exemple : 0554127845)"
global.adeli: "Veuillez respecter le format d'un numéro Adeli : 9 chiffres (exemple : 977111038)"
global.rpps: "Veuillez respecter le format d'un numéro RPPS : 11 chiffres (exemple : 10002323144)"

participant.rpps.exists: "Le numéro RPPS est déjà attribué à un autre participant"
participant.adeli.exists: "Le numéro Adéli est déjà attribué à un autre participant"