{% block vich_file_widget %}
    {{ form_label(form.file, null, {'label_attr': {'class': 'col-sm-3 control-label'}}) }}
    {{ form_errors(form.file) }}
    {{ form_widget(form.file) }}
    {% if form.delete is defined %}
        {{ form_label(form.delete) }}
        {{ form_errors(form.delete) }}
        {{ form_widget(form.delete) }}
    {% endif %}
    {% if download_uri is defined and download_uri %}
        <a href="{{ download_uri }}">{{ 'download'|trans({}, 'VichUploaderBundle') }}</a>
    {% endif %}
{% endblock %}

{% block vich_image_widget %}
    <div class="vich-image">
        {{ form_row(form.file) }}
        {% if form.delete is defined %}
        {{ form_row(form.delete) }}
        {% endif %}

        {%- if image_uri -%}
            <a href="{{ asset_helper is same as(true) ? asset(image_uri) : image_uri }}" download>
                <img style="{% if attr["max_width"] is defined %}max-width: {{ attr["max_width"] }}px;{% endif %}{% if attr["max_height"] is defined %}max-height: {{ attr["max_height"] }}px;{% endif %}" src="{{ asset_helper is same as(true) ? asset(image_uri) : image_uri }}" alt="" />
            </a>
        {%- endif -%}
        {%- if download_uri -%}
            <a href="{{ asset_helper is same as(true) ? asset(download_uri) : download_uri }}" download>
                {{ translation_domain is same as(false) ? download_label : download_label|trans({}, translation_domain) }}
            </a>
        {%- endif -%}
    </div>
{% endblock %}
