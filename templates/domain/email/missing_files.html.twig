{% extends 'domain/base_mail.html.twig' %}

{% block body %}
    <div>Bonjour,</div>
    <br>
    <div>Merci d’insérer au plus vite les documents manquants dans votre extranet pour facturation de la session suivante :</div>
    <br>
    <div>Référence : {{ item.formation.programme.reference }} </div>
    <div>Session : {{ item.formation.sessionNumber }}</div>
    <div>Titre : {{ item.formation.programme.title }}</div>
    <div>Date réunion : {{ item.formation.startDate|date("d/m/Y H:i") }}</div>
    <div>Documents : </div>
    <ul>
        {% for file in item.formation.missingFiles(item, true) %}
            <li>
                <b>{{ ("crm.comptabilite.files.labels." ~ file.type)|trans }}</b> {% if file.person is defined %} : {{ file.person.invertedFullName }}{% endif %}
            </li>
        {% endfor %}
    </ul>
    <br>
    <div><PERSON><PERSON> rappel, le document doit être inséré sous format PDF ou JPEG et ne pas excéder 128Mo.</div>
{% endblock %}