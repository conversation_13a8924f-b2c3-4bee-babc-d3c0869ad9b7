{% extends 'domain/base_mail.html.twig' %}

{% set f = item.formation %}
{% set p = f.programme %}
{% set person = item.participant.user %}
{% set coordinator = item.coordinator.person %}

{% block body %}
    <div>Bonjour,</div>
    <br>
    <div>
        Vous venez de participer {% if p.isElearning and not p.isElearningTwoUnity %} au e-learning {% else %} à la réunion {% endif %} «&nbsp;{{ p.title }}&nbsp;» qui s'est tenue ce {{ f.endDate | date('d/m/Y') }}.
    </div><br>
    <div>
        Merci de nous <strong>envoyer votre attestation sur l'honneur de mi-parcours</strong> certifiant avoir suivi le <u>parcours pré-formation</u>.
    </div><br>
    <div>
        Vous trouverez le modèle pré complété en cliquant <a style="font-weight: bold;text-decoration: underline" target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: item.formation.id, token: item.formation.token, participant: item.participant.id, person : "null" }) }}">ici</a>, que nous vous remercions de <span style="color: #e50017">dater, signer et tamponner</span> afin que l’attestation soit valide.
    </div><br>
    {% if coordinator %}
        <div>
            <strong>L'attestation est à envoyer à votre Coordinateur Régional</strong> : {{ coordinator.fullname }} par mail : {{ coordinator.email }}
        </div><br>
    {% endif %}
    <div>
        Ou à charger sur votre espace personnel :<br>
        <ul>
            <li>Onglet "Mes documents téléchargeables" de la session</li>
            <li>Puis cliquez sur "Importer l'attestation {{ f.endDate|date('Y') }} complétée et signée"</li>
        </ul>
    </div><br>
    <div>
        Pour vous connecter à votre espace personnel Eduprat, veuillez vous rendre sur le site <a href="https://extranet.eduprat.fr/login">https://extranet.eduprat.fr/login</a>.<br>
        Saisir votre identifiant (<b>{{ item.participant.identifier }}</b>) et votre mot de passe.
    </div>
{% endblock %}
