<!DOCTYPE html>
<html lang="fr">
<head>
</head>
<body>
<table cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
        <td align="center" style="padding:0 20px 20px 10px;"><table cellspacing="0" cellpadding="0" border="0" width="90%">
            <tr>
                <td align="left" style="padding:20px;"><a href="{{ url('eduprat_audit_index') }}"><img src="{{ asset('img/logo.png') }}" alt=""></a></td>
            </tr>
            <tr>
                <td bgcolor="#109199" style="padding:1px;"></td>
            </tr>
            <tr>
                <td align="left" style="padding:20px;">
                        <i>
                            Ceci est un email automatique.<br>
                            {% if not participation is defined and item is defined %}
                                {% set participation = item %}
                            {% endif %}
                            {% if participation.coordinator is defined and participation.coordinator is not null %}
                                {% set crPerson = participation.coordinator.person %}
                                {% if crPerson.email is not null %}
                                    Pour contacter directement votre Coordinateur Eduprat, merci de lui faire un email à : <a href="mailto:{{ crPerson.email }}">{{ crPerson.email }}</a>
                                {% endif %}
                            {% endif %}
                        </i><br><br>
                    {% if person is defined and person is not null and person.roles is empty and person.participant is not null %}
                        <div><b>Votre identifiant Eduprat :</b> {{ person.participant.identifier }}</div>
                        <br>
                    {% elseif emptyForCoordinator is defined and emptyForCoordinator %}
                        <div><b>Votre identifiant Eduprat :</b> XXXXXXXXXX</div>
                        <br>
                    {% endif %}
                    {% block body %}{% endblock %}
                    <br>
                    <div>Restant à votre entière disposition.</div>
                    <br>
                    <div>Bien cordialement,</div>
                    <br>
                    <div>L'Equipe Eduprat Formations</div>
                    <br>
                    {% if person is defined and person is not null and person.roles is empty %}
                        <div>Un guide de connexion à votre espace Eduprat est à votre disposition via le lien suivant : <a target="_blank" href="{{ asset(login_help_path, null, true) }}">{{ "eduprat.login" | trans | raw }}</a></div>
                        <br>
                    {% endif %}
                    <div>{{ "eduprat.title" | trans }}</div>
                    <div></div>
                    <div>{{ "eduprat.address1" | trans }}</div>
                    <div>{{ "eduprat.address2" | trans }}</div>
                    <div>{{ "eduprat.zipCity" | trans }}</div>
                    <div>Tél : <a href="tel:{{ "eduprat.href_phone" | trans }}">{{ "eduprat.phone1" | trans }}</a></div>
                    <div>Courriel : <a href="mailto:{{ "eduprat.contact" | trans }}">{{ "eduprat.contact" | trans }}</a></div>
                    {% if item is defined %}{% set participation = item %}{% endif %}
                    {% if participation is defined and participation is not null and participation.formation is defined %}
                        {% if participation.formation.programme.isClasseVirtuelle %}
                            <div>Contact technique : {{ "eduprat.technique" | trans }} <a href="tel:{{ "eduprat.technique_phone_href" | trans }}">{{ "eduprat.technique_phone" | trans }}</a></div>
                        {% endif %}
                        {# {% if participation.coordinator is defined and participation.coordinator is not null %}
                            <br>
                            <div>{{ "admin.formation.coordinator.title" | trans }}</div>
                            {% set person = participation.coordinator.person %}
                            <div>{{ person.fullname }}</div>
                            {% if person.phone is not null %}
                                <div><a href="tel:+33{{ person.phone | slice(1, -1) }}">{{ person.phone }}</a></div>
                            {% endif %}
                            {% if person.email is not null %}
                                <div><a href="mailto:{{ person.email }}">{{ person.email }}</a></div>
                            {% endif %}
                        {% endif %} #}
                    {% endif %}
                    <br>
                    <br>
                    <div style="font-style: italic">
                        Eduprat Formations traite les données recueillies pour organiser, promouvoir et gérer des formations médicales.
                        <br>
                        Pour en savoir plus sur la gestion de vos données personnelles et pour exercer vos droits, cliquez <a style="text-decoration: underline" href="{{ url('eduprat_front_personal_data') }}">ici</a>.
                        {# <br>
                        Pour en savoir plus sur les mentions légales, cliquez <a style="text-decoration: underline" href="{{ url('eduprat_front_legals') }}">ici</a> #}
                        {% block forInscription %}
                        {% endblock %}
                    </div>
                </td>
            </tr>
            <tr>
                <td bgcolor="#109199" style="padding:1px;"></td>
            </tr>
        </table></td>
    </tr>
</table>
</body>
</html>

