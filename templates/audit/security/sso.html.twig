{% if form is defined %}
    {% form_theme form 'bootstrap_3_layout.html.twig' %}
{% endif %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Admin Eduprat</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="{{ asset('admin/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('admin/dist/css/AdminLTE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/main.css') }}">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="apple-touch-icon" sizes="57x57" href="/img/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/img/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/img/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/img/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/img/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/img/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/img/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/img/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/img/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"  href="/img/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" href="/favicon.ico" />

    <meta name="msapplication-TileColor" content="#d2d6de">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#d2d6de">
</head>
<body class="hold-transition login-page">
<div class="login-box">
    <div class="login-logo">
        <img width="170" height="124" src="{{ asset('img/logo-login.png') }}" alt="Eduprat">
    </div>
    <div class="login-box-body">
        {% include 'admin/common/flashmessage.html.twig' %}
        <p class="login-box-msg">Connexion</p>
        <div id="error-block" class="callout callout-danger {% if error is empty %}hide{% endif %}">
            <p>{% if error %}{{ error.message|trans({}, 'validators') }}{% endif %}</p>
        </div>
        <form class="login-form" action="{{ path('eduprat_audit_login_check') }}" method="post">
            <div class="form-group has-feedback">
                <input id="username" name="_username" class="form-control" placeholder="{{ "login.rpps_adeli"|trans }}" value="{{ last_username }}">
            </div>
            <div class="form-group has-feedback">
                <input id="password" name="_password" type="password" class="form-control" placeholder="{{ "login.password"|trans }}">
                <i class="togglePassword fa fa-eye"></i>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <a href="{{ url('admin_user_reset',  { participant: "true" }) }}">Mot de passe oublié <br>ou création de mot de passe</a>
                </div>
                <div class="col-xs-4 pull-right">
                    <button type="submit" class="btn btn-primary btn-block btn-flat">{{ "signin"|trans }}</button>
                </div>
            </div>
            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
        </form>
    </div>
</div>

<div class="row" style="margin: 10px 0;">
    <a class="btn btn-eduprat btn-block btn-flat" href="{{ url('alienor_user_login') }}">Vous êtes Superviseur, Coordinateur, Formateur ou Webmaster ? Cliquez-ici</a>
</div>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin/bootstrap/js/bootstrap.min.js') }}"></script>
{% include 'audit/common/login_js.html.twig' %}
</body>
</html>