{% if app.user.participant is not null %}
    <div class="links-block-mobile">
         <a href="{{ url('eduprat_front_profile') }}">Mon profil</a><br>
        <a href="{{ url('eduprat_front_formations', { year: "now"|date('Y') }) }}">Mes formations</a><br>
        {% set firstFormationDate =  participation is defined and participation is not null? participation.participant.getFirstFormationDate|date('Y') : app.user.participant.getFirstFormationDate|date('Y') %}
        {% for y in "now"|date('Y')+1..firstFormationDate %}
                <a style="font-size:12px" href="{{ url('eduprat_front_formations', { year: y }) }}">{{ y }}</a><br>
        {% endfor %}
        <a href="{{ url('eduprat_front_contact') }}">Contact</a><br>
    </div>
    <a style="text-align:center" class="menu-logout-mobile" href="{{ url('participant_end_action_logout') }}"> Déconnexion</a><br><br>
{% endif %}