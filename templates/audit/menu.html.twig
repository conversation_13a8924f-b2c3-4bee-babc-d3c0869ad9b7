{% if app.user.participant is not null %}
    <div class="menu">
        <div>
            <a class="menu-link" href="{{ url('eduprat_front_profile') }}"><i class="fa fa-user icon-profile user-icon-menu" aria-hidden="true"></i>
                {% if app.user.participant is null and participation is defined and participation is not null %}
                    Bienvenue {{ participation.participant.firstname }} {{ participation.participant.lastname }}
                {% elseif app.user.participant is not null %}
                    Bienvenue {{ app.user.participant.firstname }} {{ app.user.participant.lastname }}
                {% elseif app.user is not null %}
                    Bienvenue {{ app.user.firstname }} {{ app.user.lastname }}
                {% endif %}
            </a>
        </div>
        <div class="menu-right">
            <ul class="nav navbar-nav">
                <li class="dropdown user user-menu" style="margin-top: 0.6em;">
                    <a href="#" class="dropdown-toggle formations-link" data-toggle="dropdown">
                        <span>MES FORMATIONS <i class="caret"></i></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-eduprat">
                        {% set firstFormationDate =  participation is defined and participation is not null ? participation.participant.getFirstFormationDate|date('Y') : app.user.participant.getFirstFormationDate|date('Y') %}
                        {% for y in "now"|date('Y')+1..firstFormationDate %}
                            <li class="{{ year is defined and year == y ? 'active' }} user-footer user-footer-menu">
                                <a class="btn btn-default btn-flat btn-blue btn-menu-year" href="{{ url('eduprat_front_formations', { year: y }) }}">{{ y }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </li>
            </ul>

            <a style="margin-top: 0.6em;" class="menu-right-element menu-link" href="{{ url('eduprat_front_contact') }}">BESOIN D'AIDE ?</a>
            {% if app.session.get('real_user') %}
                <a style="margin-top: 0.6em;" class="menu-right-element menu-link" title="Quitter la simulation" href="{{ url('admin_user_simulation_exit') }}"><i class="fa fa-sign-out" aria-hidden="true"></i></a>
            {% endif %}
            <a class="menu-right-element menu-link menu-logout" href="{{ url('participant_end_action_logout') }}"> Déconnexion</a>
        </div>
    </div>
{% endif %}