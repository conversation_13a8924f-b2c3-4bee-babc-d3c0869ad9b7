<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Eduprat</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="{{ asset('admin/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('admin/dist/css/AdminLTE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/front.css') }}">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="apple-touch-icon" sizes="57x57" href="/img/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/img/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/img/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/img/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/img/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/img/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/img/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/img/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/img/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"  href="/img/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" href="/favicon.ico" />

    <meta name="msapplication-TileColor" content="#d2d6de">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#d2d6de">
    <style>
        #form-container {
            background: #fff url({{ asset('img/bg-form.png') }});
            background-repeat: no-repeat;
            background-position: bottom left;
        }
    </style>
</head>
<body class="form-page">
<div class="container">
    <div id="form-container" class="bg-form">
        <header>
            <div class="text-right">{{ "general.logged_as" | trans }} <span class="eduprat">{% if app.user.participant is not null %}{{ app.user.participant.fullname }}{% else %}{{ app.user.fullname }}{% endif %}</span> - <a href="{{ url('eduprat_audit_logout') }}">{{ "general.logout" | trans }}</a></div>
        </header>
        <div id="logo">
            <a href="{{ url('eduprat_audit_index') }}"><img src="{{ asset('img/logo-form.png') }}" alt=""></a>
            <div id="contact">
                {% block contact %}
                <div>{{ "eduprat.title" | trans }}</div>
                <div><a href="tel:{{ "eduprat.href_phone" | trans }}">{{ "eduprat.phone" | trans }}</a></div>
                <div><a href="mailto:{{ "eduprat.contact" | trans }}">{{ "eduprat.contact" | trans }}</a></div>
                    {% if participation is defined %}
                        {% if participation.formation.coordinators is not null %}
                            {% set coordinators = participation.formation.coordinators %}
                            <br>
                            <div>{% if coordinators|length > 1 %}{{ "admin.formation.coordinators.title" | trans }}{% else %}{{ "admin.formation.coordinator.title" | trans }}{% endif %}</div>
                            {% for coordinator in coordinators %}
                                {% set person = coordinator.person %}
                                <div>{{ person.fullname }}</div>
                                {% if person.phone is not null %}
                                    <div><a href="tel:+33{{ person.phone | slice(1, -1) }}">{{ person.phone }}</a></div>
                                {% endif %}
                                {% if person.email is not null %}
                                    <div><a href="mailto:{{ person.email }}">{{ person.email }}</a></div>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endif %}
                {% endblock %}
            </div>
        </div>
        <div id="body">
            {% include 'admin/common/flashmessage.html.twig' %}
            {% block body %}{% endblock %}
        </div>
    </div>
</div>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin/bootstrap/js/bootstrap.min.js') }}"></script>
{% block javascripts %}{% endblock %}
</body>
</html>
