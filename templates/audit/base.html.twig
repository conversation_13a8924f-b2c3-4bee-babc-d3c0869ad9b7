<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Eduprat</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="{{ asset('admin/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet" />

    <link rel="stylesheet" href="{{ asset('admin/dist/css/AdminLTE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('admin/plugins/iCheck/line/blue.css') }}">
    <link rel="stylesheet" href="{{ asset('admin/plugins/iCheck/line/red.css') }}">
    <link rel="stylesheet" href="{{ asset('css/front.css') }}">
    {% block css %}{% endblock %}
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="apple-touch-icon" sizes="57x57" href="/img/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/img/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/img/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/img/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/img/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/img/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/img/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/img/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/img/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"  href="/img/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" href="/favicon.ico" />

    <meta name="msapplication-TileColor" content="#d2d6de">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#d2d6de">
</head>
<body class="form-page">
{% include "audit/menu.html.twig" %}
<div class="wrapper">
    <!-- Sidebar  -->

    <nav id="sidebar" class="sidebar-elearning">
        <div class="sidebar-header-mobile">
            <p class="ps-name-mobile">
                {% if app.user.participant is null and participation is defined and participation is not null %}
                    {{ participation.participant.firstname }} {{ participation.participant.lastname }}
                {% elseif app.user.participant is not null %}
                    {{ app.user.participant.firstname }} {{ app.user.participant.lastname }}
                {% elseif app.user is not null %}
                    {{ app.user.firstname }} {{ app.user.lastname }}
                {% endif %}
            </p>
            {% include "audit/menu_mobile.html.twig" %}
        </div>
        {% if (formation is defined) or (participation is defined and participation is not null) %}
            {% set f = formation is defined ? formation : participation.formation%}
            <div class="sidebar-header-desktop">
                {% if f.programme.picture %}
                    <img style="max-width: 100%;" src="{{  asset('uploads/programme/picture/'~ f.programme.picture)}}"/>
                {% endif %}
                <br>
                <div class="sidebar-header-title-elearning">{{f.programme.title}}</div>
            </div>
        {% endif %}

        {% block sidebar_current_module %}
            {% if current_module is defined %}
            <div class="sidebar-elearning-progress">
                <div class="sidebar-lesson-title">
                    <u>
                        {% if current_module == "form_presession"  %}
                            {{ ("vertical_timeline." ~ participation.formation.formTypePre) |trans }} pré
                        {% elseif current_module == "form_postsession" %}
                            {{ ("vertical_timeline." ~ participation.formation.formTypePost) |trans }} post
                        {% else %}
                            {{ ("vertical_timeline." ~ current_module) |trans }}
                        {% endif %}<br>
                    </u>
                </div>
            </div>
            {% if (current_module == "form_presession" or current_module == "form_postsession") and (participation.formation.isFormVignette or participation.formation.isFormPredefined) %}
                <ul class="formation-timeline">
                    {% if audit.nbPatients %}
                    {% for p in 1..audit.nbPatients %}
                        <li class="li {% if patientAnswereds[p] is defined %} complete {% else %}incomplete{% endif %}">
                            {% if patientAnswereds[p] is defined or patientAnswereds[p-1] is defined %}
                                <a class="sidebar-activity-link"  href="
                                    {% if app.request.get('_route') == 'eduprat_audit_show' %}
                                        {{ url('eduprat_audit_show', {id: participation.id, auditId: auditId, patient: p }) }}
                                    {% else %}
                                        {{ url('eduprat_audit_answer', {id: participation.id, auditId: auditId, patient: p }) }}
                                    {% endif %}"
                                >
                            {% else %}
                                <a class="sidebar-activity-link sidebar-no-link">
                            {% endif %}
                                    <div class="status status-sidebar">
                                    <div class="status-text status-text-sidebar {% if patient == p %}text-bold{% endif %}">
                                        Patient N°{{p}}
                                    </div>
                                    </div>
                                </a>
                        </li>
                    {% endfor %}
                    {% endif %}
                </ul>
            {% elseif current_module == "etutorat_1" %}
                <ul class="formation-timeline formation-timeline-sidebar">
                    <li class="li {% if etututo_step_1_completed is defined %}complete{% else %}incomplete{% endif %}">
                        <a class="sidebar-activity-link sidebar-no-link">
                            <div class="status status-sidebar">
                                <div class="status-text status-text-sidebar {% if etututo_step_1_completed is not defined %}text-bold{% endif %}">
                                    Positionnement<br>
                                    et attentes
                                </div>
                            </div>
                        </a>
                    </li>
                    {% if not participation.formation.isElearning %}
                        <li class="li incomplete">
                            <a class="sidebar-activity-link sidebar-no-link">
                                <div class="status status-sidebar">
                                    <div class="status-text status-text-sidebar {% if etututo_step_1_completed is defined %}text-bold{% endif %}">
                                        Cas clinique
                                    </div>
                                </div>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            {% endif %}
        {% endif %}
        {% endblock %}
        {% if satisfaction_form is defined %}
            <div class="sidebar-elearning-progress">
                <div class="sidebar-lesson-title">
                    <u>
                    <div style="text-align:center">
                        Questionnaire satisfaction<br>
                        {{ ("evaluation.titles." ~ role)|trans }}
                    </div>
                    </u>
                </div>
            </div>
        {% endif %}
        {% if toolFile is defined %}
            <ul class="formation-timeline">
                {% if fileCount %}
                    {% for i in 1..fileCount %}
                        <li class="li {% if loop.index <= participation.lastToolFile %} complete {% else %}incomplete{% endif %}">
                            <a class="sidebar-activity-link {% if loop.index > participation.lastToolFile + 1 %}sidebar-no-link {% endif %}" {% if loop.index <= participation.lastToolFile + 1 %} href="{{ url('eduprat_front_module_tool_box_file', {id: participation.id, file: loop.index }) }}"{% endif %}>
                                <div class="status status-sidebar">
                                    <div class="status-text-sidebar text-bold" style="margin-left:1.5em; text-align:center;">
                                       {{ toolFiles[loop.index -1].topoOriginalName }}
                                    </div>
                                </div>
                            </a>
                        </li>
                    {% endfor %}
                {% endif %}
            </ul>
        {% endif %}
        {% if documentsPedagogiquesFile is defined %}
            <ul class="formation-timeline">
                {% if fileCount %}
                {% for i in 1..fileCount %}
                    <li class="li {% if loop.index <= participation.lastDocumentsPedagogiquesFile %} complete {% else %}incomplete{% endif %}">
                        <a class="sidebar-activity-link {% if loop.index > participation.lastDocumentsPedagogiquesFile + 1 %}sidebar-no-link {% endif %}" {% if loop.index <= participation.lastDocumentsPedagogiquesFile + 1 %} href="{{ url('eduprat_front_module_doc_pedagogique_1_file', {id: participation.id, file: loop.index }) }}"{% endif %}>
                            <div class="status status-sidebar">
                                <div class="status-text-sidebar text-bold" style="margin-left:1.5em; text-align:center;">
                                    {{ toolFiles[loop.index -1].topoOriginalName }}
                                </div>
                            </div>
                        </a>
                    </li>
                {% endfor %}
                {% endif %}
            </ul>
        {% endif %}
        {% set validationDisabled = false %}
        {% if step is defined %}
            {% set moduleFromStep = moduleInStep(step, current_module) %}
            {% if moduleFromStep and moduleFromStep.minTime > 0 %} 
                {% set completed = moduleFromStep.completed %}
                {% set validationDisabled = not completed and moduleFromStep and moduleFromStep.minTime > 0 and moduleFromStep.spentedTime < moduleFromStep.minTime ? true : false %}
                <div class="sidebar-elearning-duration">
                    Vous êtes sur ce module depuis :<br>
                    <div id="moduleTimer" class="current-duration">00:00:00</div>
                    Temps minimum : {{(moduleFromStep.minTime / 60)}}:00
                </div>
            {% endif %}
        {% endif %}
        
    </nav>
    <button type="button" class="sidebar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
        <i class="fa fa-bars"></i>
    </button>

    <!-- Page Content  -->
    <div id="content">
        <header>
            <div class="row">
                <div class="col-lg-12 logo-eduprat">
                    {% set logoUrl = url('eduprat_audit_index') %}
                    {% if participation is defined and participation is not null %}
                        {% set logoUrl = url("eduprat_front_formations", { year: participation.formation.startDate|date("Y"), _fragment: ("participation-" ~ participation.id) }) %}
                        {% if courseManager is defined and courseManager.canAccessToModules(participation, app.user) %}
                            {% set logoUrl = url("admin_formation_show", { id: participation.formation.id }) %}
                        {% endif %}
                    {% endif %}
                    <a href="{{ logoUrl }}"><img width="82" height="60" src="{{ asset('img/eduprat-new-logo-web.png') }}" alt=""></a>
                </div>
            </div>
            {% for key, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ key }}">
                        <div class="alert-message pull-left">
                            {% if key != "success" %}<i class="fa fa-warning"></i> {% endif %}{{ message }}
                        </div>
                        <div class="alert-remove text-right">
                            <i class="fa fa-times"></i>
                        </div>
                    </div>
                {% endfor %}
            {% endfor %}
        </header>
        {% block body %}{% endblock %}
        <footer class="text-center"><a href="{{ url('eduprat_front_personal_data') }}">Données personnelles</a>
            {# |  <a href="{{ url('eduprat_front_legals') }}">Mentions légales</a> | <a href="#">CGU</a> #} |  <a href="{{ asset('img/CGV.pdf') }}" target="blank">CGV</a> {# | <a href="{{ url('eduprat_front_tutorial') }}">Tutoriel</a> #}
            | Tout droit réservé Eduprat 2021</footer>
    </div>
</div>
{% if app.user.participant %}
    <template id="gdpr">
        <h4>Gestion de mes préférences sur l’utilisation de mes données</h4>
        <p>Dans le cadre du Règlement Général sur la Protection des Données (RGPD), si vous donnez votre accord (consentement), Eduprat Formations et certains de ses partenaires pourront vous contacter.</p>
        <p>Cette page vous permet de donner ou de retirer votre consentement, soit globalement soit finalité par finalité.</p>
        <hr>
        <div class="row">
            <div class="col-lg-6">
                <p class="bold">Préférences pour tous les services</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-all-1" name="gdpr-all" data-color="blue" value="1"><label for="gdpr-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-all-2" name="gdpr-all" data-color="red" value="0"><label for="gdpr-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-lg-12">
                <p class="bold">Recevoir des courriels d’information sur nos formations</p>
            </div>
        </div>
        <div class="row">
            <br>
            <div class="col-lg-6"></div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-email-1" name="gdpr-email" data-color="blue" value="1" {% if app.user.participant.gdprAgreement == true %}checked="checked"{% endif %}><label for="gdpr-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-email-2" name="gdpr-email" data-color="red" value="0" {% if app.user.participant.gdprAgreement == false %}checked="checked"{% endif %}><label for="gdpr-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-lg-12">
                <p class="bold">Recevoir des courriers postaux d’information sur nos formations</p>
            </div>
        </div>
        <div class="row">
            <br>
            <div class="col-lg-6"></div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-post-1" name="gdpr-post" data-color="blue" value="1" {% if app.user.participant.gdprAgreementPost == true %}checked="checked"{% endif %}><label for="gdpr-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-post-2" name="gdpr-post" data-color="red" value="0" {% if app.user.participant.gdprAgreementPost == false %}checked="checked"{% endif %}><label for="gdpr-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-lg-12">
                <p class="bold">Recevoir des appels d’information sur nos formations</p>
            </div>
        </div>
        <div class="row">
            <br>
            <div class="col-lg-6"></div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-call-1" name="gdpr-call" data-color="blue" value="1" {% if app.user.participant.gdprAgreementCall == true %}checked="checked"{% endif %}><label for="gdpr-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="gdpr-call-2" name="gdpr-call" data-color="red" value="0" {% if app.user.participant.gdprAgreementCall == false %}checked="checked"{% endif %}><label for="gdpr-2">Interdire</label>
                </div>
            </div>
        </div>
    </template>
    <template id="notification">
        <h4>Gestion de mes préférences sur les notifications</h4>
        <p>Concernant l’application mobile Eduprat :</p>
        <hr>
        <div class="row">
            <div class="col-lg-6">
                <p class="bold">Préférences pour toutes les notifications</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notification-all-1" name="notification-all" data-color="blue" value="1"><label for="notification-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notification-all-2" name="notification-all" data-color="red" value="1"><label for="notification-2">Interdire</label>
                </div>
            </div>
        </div>
        <br>
        <h3>Notifications de parcours :</h3>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Confirmation d'inscription</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-inscription-1" name="notif-inscription" data-color="blue" value="1" {% if app.user.participant.notifInscription == true %}checked="checked"{% endif %}><label for="notif-inscription-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-inscription-2" name="notif-inscription" data-color="red" value="0" {% if app.user.participant.notifInscription == false %}checked="checked"{% endif %}><label for="notif-inscription-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Accessibilité questionnaire</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-surveyOpen-1" name="notif-surveyOpen" data-color="blue" value="1" {% if app.user.participant.notifSurveyOpen == true %}checked="checked"{% endif %}><label for="notif-surveyOpen-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-surveyOpen-2" name="notif-surveyOpen" data-color="red" value="0" {% if app.user.participant.notifSurveyOpen == false %}checked="checked"{% endif %}><label for="notif-surveyOpen-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Relance questionnaire</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-remindSurvey-1" name="notif-remindSurvey" data-color="blue" value="1" {% if app.user.participant.notifRemindSurvey == true %}checked="checked"{% endif %}><label for="notif-remindSurvey-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-remindSurvey-2" name="notif-remindSurvey" data-color="red" value="0" {% if app.user.participant.notifRemindSurvey == false %}checked="checked"{% endif %}><label for="notif-remindSurvey-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Rappel de la formation présentielle</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-remindSession-1" name="notif-remindSession" data-color="blue" value="1" {% if app.user.participant.notifRemindSession == true %}checked="checked"{% endif %}><label for="notif-remindSession-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-remindSession-2" name="notif-remindSession" data-color="red" value="0" {% if app.user.participant.notifRemindSession == false %}checked="checked"{% endif %}><label for="notif-remindSession-2">Interdire</label>
                </div>
            </div>
        </div>
        <br>
        <h3>Notifications d'information :</h3>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Nouvelle formation disponible</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-newSession-1" name="notif-newSession" data-color="blue" value="1" {% if app.user.participant.notifNewSession == true %}checked="checked"{% endif %}><label for="notif-newSession-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-newSession-2" name="notif-newSession" data-color="red" value="0" {% if app.user.participant.notifNewSession == false %}checked="checked"{% endif %}><label for="notif-newSession-2">Interdire</label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <br>
            <div class="col-lg-6">
                <p class="bold">Changement dans votre formation</p>
            </div>
            <div class="hidden-lg">
                <br>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-sessionChange-1" name="notif-sessionChange" data-color="blue" value="1" {% if app.user.participant.notifSessionChange == true %}checked="checked"{% endif %}><label for="notif-sessionChange-1">Autoriser</label>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="form-row">
                    <input type="radio" id="notif-sessionChange-2" name="notif-sessionChange" data-color="red" value="0" {% if app.user.participant.notifSessionChange == false %}checked="checked"{% endif %}><label for="notif-sessionChange-2">Interdire</label>
                </div>
            </div>
        </div>
    </template>
    <template id="gdpr-notification">
        <p>Bienvenue sur votre compte Eduprat !</p>
        <p>Vous pouvez désormais consulter et/ou modifier vos préférences concernant l’utilisation de vos données, dans l’onglet « <b>Mon profil</b> ».</p>
        <p>Bonne navigation !</p>
    </template>
    <template id="inactive-popup">
        <p>Êtes-vous toujours présent ?</p>
    </template>
{% endif %}
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin/bootstrap/js/bootstrap.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/popper.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/shepherd.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin/plugins/iCheck/icheck.js') }}"></script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<script>

	var participant = {% if app.user.participant %}{{ app.user.participant.id }}{% else %}null{% endif %};
	var isSimulating = {{ app.session.has('real_user') ? "true" : "false" }};
	var isTutorial = {{ tutorial is defined ? "true" : "false" }};
	var gdprAgreement = {% if app.user.participant %}{{ app.user.participant.gdprAgreement is null ? 'null' : app.user.participant.gdprAgreement ? 'true' : 'false' }}{% else %}'true'{% endif %};
	var gdprAgreementPost = {% if app.user.participant %}{{ app.user.participant.gdprAgreementPost is null ? 'null' : app.user.participant.gdprAgreementPost ? 'true' : 'false' }}{% else %}'true'{% endif %};
	var gdprAgreementCall = {% if app.user.participant %}{{ app.user.participant.gdprAgreementCall is null ? 'null' : app.user.participant.gdprAgreementCall ? 'true' : 'false' }}{% else %}'true'{% endif %};
	var gdprNotification = {% if app.user.participant %}{{ app.user.participant.gdprAgreementHistory is null ? 'true' : 'false' }}{% else %}'false'{% endif %};
    var notifInscription = {% if app.user.participant %} {{ app.user.participant.notifInscription is null ? 'null' : app.user.participant.notifInscription ? 'true' : 'false' }}{% else %}'true'{% endif %};
    var notifSurveyOpen = {% if app.user.participant %} {{ app.user.participant.notifSurveyOpen is null ? 'null' : app.user.participant.notifSurveyOpen ? 'true' : 'false' }}{% else %}'true'{% endif %};
    var notifRemindSurvey = {% if app.user.participant %} {{ app.user.participant.notifRemindSurvey is null ? 'null' : app.user.participant.notifRemindSurvey ? 'true' : 'false' }}{% else %}'true'{% endif %};
    var notifRemindSession = {% if app.user.participant %} {{ app.user.participant.notifRemindSession is null ? 'null' : app.user.participant.notifRemindSession ? 'true' : 'false' }}{% else %}'true'{% endif %};
    var notifNewSession = {% if app.user.participant %} {{ app.user.participant.notifNewSession is null ? 'null' : app.user.participant.notifNewSession ? 'true' : 'false' }}{% else %}'true'{% endif %};
    var notifSessionChange = {% if app.user.participant %} {{ app.user.participant.notifSessionChange is null ? 'null' : app.user.participant.notifSessionChange ? 'true' : 'false' }}{% else %}'true'{% endif %};
    let timeSupp = 0;
    let moduleTimeSupp = 0;
    var isFormPostAccessible = {{ participation is defined and participation.formation is defined and participation.formation.isFormPostAccessible ? 'true' : 'false' }};
    let refresModuleTimeUrl = {% if current_module is defined %}'{{ url('eduprat_front_refresh_module_time', {id: participation.id, moduleId: current_module }) }}'{% else %}"false"{% endif %};
    var minModuleTime = {{ moduleFromStep ? moduleFromStep.minTime : 0 }};
    let currentModuleTime = {{ moduleFromStep ? moduleFromStep.spentedTime : "false" }};

    var timerModuleElement = document.getElementById('moduleTimer');
    if (timerModuleElement) {
        var temps=new Date();
        temps.setTime(currentModuleTime*1000);
        let moduleHeures = temps.getHours()-1;
        let moduleMinutes = temps.getMinutes();
        let ModuleSecondes = temps.getSeconds();
        moduleHeures = moduleHeures < 10 ? "0" + moduleHeures : moduleHeures
        moduleMinutes = moduleMinutes < 10 ? "0" + moduleMinutes : moduleMinutes
        ModuleSecondes = ModuleSecondes < 10 ? "0" + ModuleSecondes : ModuleSecondes
        timerModuleElement.innerText = moduleHeures + ":" + moduleMinutes + ":" + ModuleSecondes;
    }

	$(document).ready(function() {

		$('[data-toggle="tooltip"]').not('.timeline-disabled').tooltip({
            placement: function() {
            	return "top";
            }
        });

		$("[data-toggle=tooltip]").hover(function(){
			if ($(window).width() > 767) {
				$('.tooltip').css('top','-63px');
				$('.tooltip').css('left','-88px');
            } else {
				$('.tooltip').css('top','-65px');
				$('.tooltip').css('left','-68px');
			}
		});

		$('.alert-remove').click(function (){
			$(this).closest('.alert').remove();
        });

		$('.sidebar-toggle').click(function () {
			console.log('a');
			$('#sidebar').toggleClass('active', !$('#sidebar').hasClass('active'));
        });

		$(document).on('click', 'a[href="#!"]', function(e) {
			e.preventDefault();
        });

		$(document).on('click', '.module-clear', function(e) {
			e.preventDefault();
			e.stopPropagation();
			window.location = $(this).data("href");
		});

		$("#valid-audit.valid-alert").click(function(e) {
			if (!confirm("En cas de validation les réponses ne seront plus modifiables.")) {
				e.preventDefault();
				return false;
			}
		});

		$(document).on("mousedown", ".download-file", function(e) {
			var $this = $(this);
			if ((e.which === 1 || e.which === 2) && (!$(this).hasClass("disabled") && !$this.hasClass("link-disabled"))) {
				saveDownloadDate($this.data('type'), $this.data("participation"));
			}
		});

        $(document).on("mousedown", "#download-file-topo", function(e) {
			var $this = $(this);
			if ((e.which === 1 || e.which === 2) && (!$(this).hasClass("disabled") && !$this.hasClass("link-disabled"))) {
				saveDownloadDate($this.data('type'), $this.data("participation"));
			}
		});


		if (gdprNotification && !isSimulating && participant) {
			showGDPRNotification();
		}

        if (isFormPostAccessible && refresModuleTimeUrl != "false" && timerModuleElement && minModuleTime > 0) {
            function incrementeModuleTime() {
	            var moduleTemps=new Date();
                if (moduleTimeSupp == 60) {
                    jQuery.ajax({
                        url: refresModuleTimeUrl,
                        type: "POST",
                        data: "",
                        success: function(data, textStatus, xhr) {
                            currentModuleTime = data.currentModuleTime
                            moduleTimeSupp = 0;
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.log(errorThrown);
                        }
                    });
                }
               
                moduleTemps.setTime((currentModuleTime+moduleTimeSupp)*1000);
                let moduleHeures = moduleTemps.getHours()-1;
                let moduleMinutes = moduleTemps.getMinutes();
                let moduleSecondes = moduleTemps.getSeconds();
                moduleHeures = moduleHeures < 10 ? "0" + moduleHeures : moduleHeures
                moduleMinutes = moduleMinutes < 10 ? "0" + moduleMinutes : moduleMinutes
                moduleSecondes = moduleSecondes < 10 ? "0" + moduleSecondes : moduleSecondes
                timerModuleElement.innerText = moduleHeures + ":" + moduleMinutes + ":" + moduleSecondes;

                

                if (minModuleTime && (minModuleTime <= (currentModuleTime+moduleTimeSupp))) {
                    $('.validationDisabled').each(function(){
						if ($(this).hasClass("disabled")) {
                            $(this).removeClass("disabled");
                            $(this).removeClass("validationDisabled");
                            $(this).prop("disabled", false)
                        }
					});
                }
                moduleTimeSupp++
            }
            setInterval(incrementeModuleTime, 1000)
        }

        $('.submit-module').click(function (){
            if ($(this).attr('href') && !$(this).hasClass('etutorat-1')) {
                window.location.href = $(this).attr('href');
            }
        });

        

	});

	function saveDownloadDate(type, participation) {
		var url = "{{ url("participant_date_download", {type: "__type__", participation: "__participation__"}) }}";
		url = url.replace('__type__', type);
		url = url.replace('__participation__', participation);
		navigator.sendBeacon(url);
	}

	function showGDPR() {

		var tour = new Shepherd.Tour({
			defaultStepOptions: {
				classes: 'shepherd-theme-arrows',
				scrollTo: true
			}
		});

		tour.addStep('example-step', {
			text: $('#gdpr').html(),
			classes: 'step-tour-gdpr',
			when: {
				show: function() {
					$('.step-tour-gdpr input').each(function(){
						var self = $(this),
							label = self.next(),
							label_text = label.text(),
                            color = self.data('color');

						label.remove();
						self.iCheck({
							radioClass: 'iradio_line-custom iradio_line-' + color,
							insert: '<div class="icheck_line-icon"></div>' + label_text
						});
					});

					$('#gdpr-email-1').prop('checked', gdprAgreement == true);
					$('#gdpr-email-2').prop('checked', gdprAgreement == false);
					$('#gdpr-post-1').prop('checked', gdprAgreementPost == true);
					$('#gdpr-post-2').prop('checked', gdprAgreementPost == false);
					$('#gdpr-call-1').prop('checked', gdprAgreementCall == true);
					$('#gdpr-call-2').prop('checked', gdprAgreementCall == false);

					$('.step-tour-gdpr input').iCheck('update');

					$('#gdpr-all-1').on('ifChecked', function(event){
						$('#gdpr-email-1').prop('checked', true);
						$('#gdpr-post-1').prop('checked', true);
						$('#gdpr-call-1').prop('checked', true);
						$('.step-tour-gdpr input').iCheck('update');
					});

					$('#gdpr-all-2').on('ifChecked', function(event){
						$('#gdpr-email-2').prop('checked', true);
						$('#gdpr-post-2').prop('checked', true);
						$('#gdpr-call-2').prop('checked', true);
						$('.step-tour-gdpr input').iCheck('update');
					});

					["#gdpr-email-1","#gdpr-email-2","#gdpr-post-1","#gdpr-post-2","#gdpr-call-1","#gdpr-call-2"].forEach(function(selector) {
						$(selector).on('ifChecked', function(event){
							$('#gdpr-all-1').iCheck('uncheck');
							$('#gdpr-all-2').iCheck('uncheck');
						});
                    });
				}
			},
			buttons: [
				{
					text: 'En savoir plus',
					events: {
						click: function(e) {
							e.preventDefault();
							var url = '{{ url("eduprat_front_personal_data") }}';
							$("<a>").attr("href", url).attr("target", "_blank")[0].click();
						}
					}
				},
				{
					text: 'Enregistrer',
					events: {
						click: function() {
							var email = $('[name=gdpr-email]:checked').val();
							var post = $('[name=gdpr-post]:checked').val();
							var call = $('[name=gdpr-call]:checked').val();
							setGdrpAgreement(email, post, call).then(Shepherd.activeTour.complete);
						}
					}
				}
			]
		});

		tour.start();
	}

	function showNotification() {

		var tour = new Shepherd.Tour({
			defaultStepOptions: {
				classes: 'shepherd-theme-arrows',
				scrollTo: true
			}
		});

		tour.addStep('example-step', {
			text: $('#notification').html(),
			classes: 'step-tour-notif',
			when: {
                show: function() {
					$('.step-tour-notif input').each(function(){
						var self = $(this),
							label = self.next(),
							label_text = label.text(),
                            color = self.data('color');

						label.remove();
						self.iCheck({
							radioClass: 'iradio_line-custom iradio_line-' + color,
							insert: '<div class="icheck_line-icon"></div>' + label_text
						});
					});


                    notifInscription = notifInscription == null ? true : notifInscription;
                    notifSurveyOpen = notifSurveyOpen == null ? true : notifSurveyOpen;
                    notifRemindSurvey = notifRemindSurvey == null ? true : notifRemindSurvey;
                    notifRemindSession = notifRemindSession == null ? true : notifRemindSession;
                    notifNewSession = notifNewSession == null ? true : notifNewSession;
                    notifSessionChange = notifSessionChange == null ? true : notifSessionChange;

					$('#notif-inscription-1').prop('checked', notifInscription == true);
					$('#notif-inscription-2').prop('checked', notifInscription == false);
					$('#notif-surveyOpen-1').prop('checked', notifSurveyOpen == true);
					$('#notif-surveyOpen-2').prop('checked', notifSurveyOpen == false);
					$('#notif-remindSurvey-1').prop('checked', notifRemindSurvey == true);
					$('#notif-remindSurvey-2').prop('checked', notifRemindSurvey == false);
					$('#notif-remindSession-1').prop('checked', notifRemindSession == true);
					$('#notif-remindSession-2').prop('checked', notifRemindSession == false);
					$('#notif-newSession-1').prop('checked', notifNewSession == true);
					$('#notif-newSession-2').prop('checked', notifNewSession == false);
					$('#notif-sessionChange-1').prop('checked', notifSessionChange == true);
					$('#notif-sessionChange-2').prop('checked', notifSessionChange == false);

					$('.step-tour-notif input').iCheck('update');

                    $('#notification-all-2').on('ifChecked', function(event){
                        $('#notif-inscription-2').prop('checked', true);
                        $('#notif-surveyOpen-2').prop('checked', true);
                        $('#notif-remindSurvey-2').prop('checked', true);
                        $('#notif-remindSession-2').prop('checked', true);
                        $('#notif-newSession-2').prop('checked', true);
                        $('#notif-sessionChange-2').prop('checked', true);
                        $('.step-tour-notif input').iCheck('update');
					});

					$('#notification-all-1').on('ifChecked', function(event){
                        $('#notif-inscription-1').prop('checked', true);
                        $('#notif-surveyOpen-1').prop('checked', true);
                        $('#notif-remindSurvey-1').prop('checked', true);
                        $('#notif-remindSession-1').prop('checked', true);
                        $('#notif-newSession-1').prop('checked', true);
                        $('#notif-sessionChange-1').prop('checked', true);
                        $('.step-tour-notif input').iCheck('update');
					});

                    ["#notif-inscription-1", "#notif-surveyOpen-1", "#notif-remindSurvey-1", "#notif-remindSession-1", "#notif-newSession-1", "#notif-sessionChange-1", "#notif-inscription-2", "#notif-surveyOpen-2", "#notif-remindSurvey-2", "#notif-remindSession-2", "#notif-newSession-2", "#notif-sessionChange-2"].forEach(function(selector) {
						$(selector).on('ifChecked', function(event){
							$('#notification-all-1').iCheck('uncheck');
							$('#notification-all-2').iCheck('uncheck');
						});
                    });
				}
			},
			buttons: [
				{
					text: 'En savoir plus',
					events: {
						click: function(e) {
							e.preventDefault();
							var url = '{{ url("eduprat_front_personal_data") }}';
							$("<a>").attr("href", url).attr("target", "_blank")[0].click();
						}
					}
				},
				{
					text: 'Enregistrer',
					events: {
						click: function() {
                            var inscription = $('[name=notif-inscription]:checked').val()
                            var surveyOpen = $('[name=notif-surveyOpen]:checked').val()
                            var remindSurvey = $('[name=notif-remindSurvey]:checked').val()
                            var remindSession = $('[name=notif-remindSession]:checked').val()
                            var newSession = $('[name=notif-newSession]:checked').val()
                            var sessionChange = $('[name=notif-sessionChange]:checked').val()
							
							setNotificationAgreement(inscription, surveyOpen, remindSurvey, remindSession, newSession ,sessionChange).then(Shepherd.activeTour.complete);
						}
					}
				}
			]
		});

		tour.start();
	}

	function setGdrpAgreement(email, post, call) {

		gdprAgreement = (email === "1" ? true : false);
		gdprAgreementPost = (post === "1" ? true : false);
		gdprAgreementCall = (call === "1" ? true : false);

		return jQuery.ajax({
			url: '{{ url("eduprat_front_gdpr") }}',
			type: 'POST',
			data: { email: gdprAgreement, post: gdprAgreementPost, call: gdprAgreementCall }
		});
	}

	function setNotificationAgreement(inscription, surveyOpen, remindSurvey, remindSession, newSession ,sessionChange) {


        notifInscription = (inscription === "1" ? true : false);
        notifSurveyOpen = (surveyOpen === "1" ? true : false);
        notifRemindSurvey = (remindSurvey === "1" ? true : false);
        notifRemindSession = (remindSession === "1" ? true : false);
        notifNewSession = (newSession === "1" ? true : false);
        notifSessionChange = (sessionChange === "1" ? true : false);
		return jQuery.ajax({
	        url: '{{ url("eduprat_front_notification") }}',
			type: 'POST',
			data: { notifInscription : notifInscription, notifSurveyOpen : notifSurveyOpen, notifRemindSurvey : notifRemindSurvey, notifRemindSession : notifRemindSession, notifNewSession : notifNewSession, notifSessionChange : notifSessionChange }
		});
	}

	function showGDPRNotification() {

		var tour = new Shepherd.Tour({
			defaultStepOptions: {
				classes: 'shepherd-theme-arrows',
				scrollTo: true
			}
		});

		tour.addStep('example-step', {
			text: $('#gdpr-notification').html(),
			classes: 'step-tour-gdpr step-tour-gdpr-notification, step-tour-notif',
			buttons: [
				{
					text: 'OK',
					events: {
						click: function() {
							setGdrpNotificationAccepted().then(Shepherd.activeTour.complete);
						}
					}
				}
			]
		});

		tour.start();
	}

	function setGdrpNotificationAccepted() {
		return jQuery.ajax({
			url: '{{ url("eduprat_front_gdpr_notification") }}',
			type: 'POST',
		});
	}

	function setIdleTimeout(millis, onIdle, onUnidle) {
		var timeout = 0;
		startTimer();

		function startTimer() {
			timeout = setTimeout(onExpires, millis);
			document.addEventListener("mousemove", onActivity);
			document.addEventListener("keypress", onActivity);
		}

		function onExpires() {
			timeout = 0;
			onIdle();
			if (timeout) clearTimeout(timeout);
		}

		function onActivity() {
			if (timeout) clearTimeout(timeout);
			else onUnidle();
			//since the mouse is moving, we turn off our event hooks for 1 second
			document.removeEventListener("mousemove", onActivity);
			document.removeEventListener("keypress", onActivity);
			setTimeout(startTimer, 1000);
		}
	}

	function redirectIfInactive(millis = 105 * 60 * 1000) {
		setIdleTimeout(millis, function() {
			if (typeof Shepherd.activeTour === "undefined" || Shepherd.activeTour === null) {
				var tour = new Shepherd.Tour({
					defaultStepOptions: {
						classes: 'shepherd-theme-arrows',
					}
				});

				var timeout;

				tour.addStep('inactive-step', {
					text: $('#inactive-popup').html(),
					classes: 'step-tour-inactive',
					scrollTo: {
						behavior: 'smooth',
						block: 'center'
					},
					scrollToHandler: function() {
						document.querySelector(".step-tour-inactive").scrollIntoView({block: "center"});
                    },
					useModalOverlay: true,
					when: {
						show: function() {
							timeout = setTimeout(function() {
								var logoutUrl = '{{ url("participant_end_action_logout") }}';
								window.location.replace(logoutUrl);
							}, 60000);
						}
					},
					buttons: [
						{
							text: 'Oui, je poursuis ma formation',
							events: {
								click: function(e) {
									e.preventDefault();
									if (timeout) clearTimeout(timeout);
									Shepherd.activeTour.complete();
								}
							}
						}
					]
				});
                document.exitFullscreen()
				tour.start();
			}
        }, function() {});

		window.addEventListener('unload', endCurrentAction, false);

		function endCurrentAction() {
			navigator.sendBeacon('{{ url("participant_end_action") }}');
		}
    }

</script>
{% block javascripts %}{% endblock %}
</body>
</html>
