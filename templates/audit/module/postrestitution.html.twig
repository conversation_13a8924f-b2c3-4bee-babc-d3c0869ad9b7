{% extends 'audit/module/base.html.twig' %}

{% block module_css %}
    <link href="{{ asset('css/commun_pdf_front.css') }}" rel="stylesheet">
    <link href="{{ asset('css/pdf/restitution_audit_groupe_individuelle.css') }}" rel="stylesheet">
    <link href="{{ asset('css/pdf_front.css') }}" rel="stylesheet">
{% endblock %}

{% block module_body %}
    <div class="alert alert-danger alert-obligatoire module-notif">Ce module est obligatoire.</div>
    {% set summaryIndex = 0 %}

    <div class="page-content">

        <div class="text-big">
            Vous trouverez ci-dessous votre résultat {{ ("front.label.formType." ~ participation.formation.formTypePost ~ ".a") | trans }} post formation et une comparaison avec les autres participants de la session.
        </div>

        <p class="text-big">Nombre de répondants : {{ respondents }}</p>

        {% if formation.isFormPredefined or formation.isFormPostVignette %}
            <p class="text-big" style="font-size: 18px">Vous avez <span class="bold">{{ globalScores[2]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePost ~ ".plural") | trans }} post formation.</p>
        {% endif %}

        <div class="nav-tabs-front mtl">
            <ul class="nav nav-tabs">
                <li class="active"><a href="#tab_theme" data-toggle="tab" aria-expanded="true">Score moyen par thème</a></li>
                <li><a href="#tab_indicator" data-toggle="tab" aria-expanded="false">Analyse par indicateur</a></li>
                {% if formation.isFormPostVignette %}
                    <li><a href="#tab_analyse" data-toggle="tab" aria-expanded="false">Analyse par question</a></li>
                {% endif %}
                {% if formation.isFormPostDefault %}
                    <li><a href="#tab_criteres" data-toggle="tab" aria-expanded="false">Nombre de bonnes réponses par question</a></li>
                {% endif %}
                {% if formation.isFormPostDefault or formation.isFormPostVignette %}
                    <li><a href="#tab_axes" data-toggle="tab" aria-expanded="false">{{ "restitution.synthese" | trans }}</a></li>
                {% endif %}
            </ul>
            <div class="tab-content tab-eduprat">
                <div class="tab-pane active" id="tab_theme">
                    <div id="spiderweb" data-width="auto"></div>
                </div>
                <div class="tab-pane" id="tab_indicator">
                    {% include "pdf/section/indicators.html.twig" with {'show1' : false, 'showAvg1': false, showTitle: false } %}
                </div>
                {% if formation.isFormPostVignette %}
                    <div class="tab-pane" id="tab_analyse">
                        {% include "pdf/section/analysePost.html.twig" with { auditId: 2, showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isFormPostDefault %}
                    <div class="tab-pane" id="tab_criteres">
                        {% include "pdf/section/criteresPost.html.twig" with { showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isFormPostDefault or formation.isFormPostVignette %}
                    <div class="tab-pane" id="tab_axes">
                        {% include "pdf/section/axesPost.html.twig" with { auditId: 1, showTitle: false } %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="page-footer">
        {% if validationDisabled %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
        <div class="text-right">
            <a id="download-file" data-type="restitution" data-participation="{{ participation.id }}" target="_blank" href="{{ url("pdf_restitution_audit_pdf", { id: participation.id, token: participation.token }) }}" class="btn btn-eduprat btn-medium download-file btn-secondaire">Télécharger le PDF</a>
            <div class="mll" style="display: inline-block">
                <a id="validate-step" href="{{ url("eduprat_front_formation_restitution_validate", { id: participation.id }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
            </div>
            {% if courseManager.canAccessToModules(participation, app.user) %}
                <div class="mll" style="display: inline-block">
                    <a href="{{ url("eduprat_front_formation_restitution_validate", { id: participation.id, redirect: "admin" }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}">{{ "front.btn.validate_admin"|trans }}</a>
                </div>
            {% endif %}
        </div>
        {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
        {% if modulesLeftSentence %}
            <div class="text-right font-16">
            {{ modulesLeftSentence|raw }}
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block module_javascripts %}
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/highcharts-more.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
    <script src="{{ asset('js/radarChart.js') }}"></script>
    {% include "pdf/script/postrestitution.html.twig" %}
{% endblock %}