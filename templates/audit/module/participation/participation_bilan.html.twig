{% extends 'audit/module/participation/base_tcs.html.twig' %}

{% block title %}Réponse du participant{% endblock %}

{% block content_tcs %}
    <table class="table-action rwd-table tcs-bilan">
        <thead>
            <tr class="eduprat-color">
                <th class="eduprat-color" colspan="3">
                    {{ question.groupeQuestion.description|striptags|raw }}
                </th>
            </tr>
            {% if question.groupeQuestion.descriptionImage %}
                <tr>
                    <th colspan="3" style="background-color:white;">
                       <img class="tcs-picture btn-open-modale" src="{{ asset('uploads/questionTCSPicture/'~ question.groupeQuestion.descriptionImage) }}" alt="" style="display: block; margin:auto; padding:1rem; max-height:400px" width="auto" />
                    </th>
                </tr>
                <div id="selectionModal" class="modal-tcs">
                    <div class="modal-tcs-content">
                        <span class="close">&times;</span>
                            <img style="width:95%" src="{{ asset('uploads/questionTCSPicture/'~ question.groupeQuestion.descriptionImage) }}" alt=""/>
                    </div>
                </div>
            {% endif %}
            <tr class="rwd-table--hide-mobile">
                <th width="33%">Si vous pensez...</th>
                <th width="33%">Et qu'alors vous trouvez...</th>
                <th width="33%">Votre hypothèse ou option en est...</th>
            </tr>
        </thead>
        <tbody>
        <tr>
            <td>
                <span class="tcs-only-mobile question-responsive">Question {{ question.groupeQuestion.numeroQuestion(question) }} : <br></span>
                <span class="tcs-only-mobile">Si vous pensiez à : </span> {{ question.libelleSiVousPensiez|raw }}
            </td>
            <td><span class="tcs-only-mobile">et qu'alors vous trouvez : </span> {{ question.libelleEtQuAlorsVousTrouvez|raw }}</td>
            <td class="reponse">
                <span class="tcs-only-mobile">votre hypothèse ou option en est : </span><br class="tcs-only-mobile">
                {% for answer in question.reponses %}
                    <input
                        type="radio" disabled name="reponsesExperts" id="reponsesExperts{{ answer.id }}" value="{{ answer.id }}"
                        {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked{% endif %}
                    >
                    <label for="reponsesExperts{{ answer.id }}">{{ answer.reponse }}</label>
                    <br>
                {% endfor %}
            </td>
        </tr>
        </tbody>
    </table>
    <span class="bold">Justifiez votre réponse :</span><br>
    {{ participationAnswerTCS.justification }}

    <div class="tcs-results">
        <span class="underline bold">Résultats :</span>
        <div class="tcs-result-items">
            <div class="tcs-result-item tcs-result-item-experts">
                <span class="bold">Réponses des {{ question.expertsAnswers|length }} experts</span>
            </div>
            <div class="tcs-result-item">
                <span class="bold">Votre réponse</span>
            </div>
        </div>

        {% for answer in question.reponses %}
            <div class="tcs-result-items">
                <div class="tcs-result-item reponse">
                    <input type="radio" disabled name="reponsesExperts-{{ loop.index }}" id="reponsesExperts{{ answer.id }}" value="{{ answer.id }}"
                       {% if question.displayAnswerChecked(answer) %}checked{% endif %}
                    >
                    {% if question.displayAnswerChecked(answer) %}
                        <label class="marge-left" for="reponsesExperts{{ answer.id }}">{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}</label>
                    {% endif %}
                </div>
                <div class="tcs-result-item reponse votre-reponse">
                    <input type="radio" disabled name="reponsesParticipation" id="answer-participation-{{ answer.id }}" value="{{ answer.id }}"
                           {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked{% endif %}
                    >
                    <div class="votre-reponse-text">
                        <label for="answer-participation-{{ answer.id }}">Votre hypothèse ou option en est {{ answer.reponse|lower }}</label>
                        <div class="answer-bilan-experts-responsive">
                            {% if question.displayAnswerChecked(answer) %}
                                {{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }} expert{% if question.countAnswer(answer) > 1 %}s ont{% else %} a{% endif %} donné cette réponse
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <div class="tcs-answers-experts">
        <p class="justification-panel">
            <span class="underline bold">Justification du panel d'experts sur la Question {{ question.groupeQuestion.numeroQuestion(question) }}:</span><br>
        </p>
        <div class="eduprat">
            <p class="justification-panel">
                Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}
            </p>
        </div>
        <div class="tcs-answers-experts-items">
            {% for expertsAnswer in question.expertsAnswersGroupByReponse %}
                <div class="tcs-answers-experts-question">
                    <p class="expert-ayant-repondu">
                        <span class="underline">Expert{% if expertsAnswer|length > 1 %}s{% endif %} ayant répondu :</span> <span class="bold">Votre hypothèse ou option en est {{ expertsAnswer[0].reponseTCS.reponse|lower }}</span>
                    </p>
                    <div class="tcs-answers-experts-items">
                        {% for expertAnswer in expertsAnswer %}
                            <div class="tcs-desktop">
                                <div class="tcs-answers-experts-item">
                                    <i class="fa fa-user-circle fa-2x" aria-hidden="true"></i><br class="hide-desktop">
                                    <span class="justification" style="position:absolute; margin-left:50px">Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :&nbsp;</span> <span style="margin-left:100px">{% if expertAnswer.justification %} {{ expertAnswer.justification }} {% else %} Pas de justification apportée {% endif %}</span>
                                </div>
                            </div>
                            <div class="tcs-mobile">
                                <div class="tcs-answers-experts-item">
                                    <i class="fa fa-user-circle fa-2x" aria-hidden="true"></i><br class="hide-desktop">
                                    <p class="justification">Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :&nbsp;</p>
                                </div>
                                <p class="justification-detail">{% if expertAnswer.justification %}{{ expertAnswer.justification }}{% else %}Pas de justification apportée{% endif %}</p>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    <div class="page-footer mtl" style="display: flex; justify-content: space-between;">
        <div class="text-left">
            {% if previous_question %}
                <a class="btn btn-eduprat btn-medium" href="{{ path('app_admin_tcs_participation_answer', {'participation': participation.id, 'questionTCS': previous_question.id}) }}">Retour</a>
            {% endif %}
        </div>
        <div class="text-right">
            {% if question.groupeQuestion.isLastQuestion(question) %}
                <a class="btn btn-eduprat btn-medium" href="{{ path('app_admin_tcs_participation_synthese_educative', {'participation': participation.id, 'questionTCS': question.id}) }}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
            {% else %}
                <a class="btn btn-eduprat btn-medium" href="{{ path('app_admin_tcs_participation_next_question', {'participation': participation.id, 'questionTCS': question.id}) }}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
            {% endif %}
        </div>
    </div>
{% endblock %}
