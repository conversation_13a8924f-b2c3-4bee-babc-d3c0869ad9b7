{% extends 'audit/module/base.html.twig' %}

{% block module_body %}
    <div class="page-content">

        {{ form_start(form) }}
        {% if editable %}
            <div class="alert alert-message alert-warning">
                <div class="icon">
                    <i class="fa fa-lightbulb-o" aria-hidden="true"></i>
                </div>
                <div class="text">
                    Vous avez jusqu'au <span class="text-bold">{{ formation.closingDate|format_datetime(locale='fr', pattern="EEEE d LLLL y 23'h'59") }}</span> pour la modifier
                </div>
            </div>
        {% endif %}

        <table class="table-action rwd-table">
            <tr>
                <th style="width: 25%; background: #f3f3f3"></th>
                <th style="width: 25%">Points clés retenus<br>Points d'améliorations actés</th>
                <th style="width: 25%">Points pouvant encore être améliorés après la formation</th>
                <th style="width: 25%">Moyens pour y parvenir<br>(ex : nouvelles formations, discussion avec des pairs, lecture d'articles ...)</th>
            </tr>
            {% for subForm in form.ficheProgressions %}
                <tr class="action-row">
                    <td>
                        <span class="text-bold" style="font-size: 17px;">Cas clinique {{ subForm.vars.data.patient.patient }} :</span>
                        <br>
                        {{ subForm.vars.data.patient.description|striptags|length > 350 ?  subForm.vars.data.patient.description|striptags|slice(0, 350) ~ '...' : subForm.vars.data.patient.description }} 
                        <br>
                        <a target="_blank" href="{{ url("eduprat_front_formation_module", { id: participation.id, module: "restitution", _fragment: ("cas-patient-" ~ subForm.vars.data.patient.patient) }) }}">Revoir le cas patient {{ subForm.vars.data.patient.patient }}</a>
                    </td>
                    <td data-th="Points clés retenus / Points d'améliorations actés">{{ form_widget(subForm.pointsCles) }}</td>
                    <td data-th="Points pouvant encore être améliorés après la formation">{{ form_widget(subForm.ameliorations) }}</td>
                    <td data-th="Moyens pour y parvenir (ex : nouvelles formations, discussion avec des pairs, lecture d'articles ...)">{{ form_widget(subForm.moyens) }}</td>
                </tr>
            {% endfor %}
        </table>

        <br><br><br>
        <div>
            <div class="hidden">
                {{ form_rest(form) }}
            </div>
            <div class="page-footer">
                {% if validationDisabled %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
                <div class="text-right">
                    <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium submit-module {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="next">
                        Suivant <i class="fa-solid fa-chevron-right next-icon"></i>
                        {# {% if editable %}
                            Enregistrer et passer au module suivant
                        {% else %}
                            Passer au module suivant
                        {% endif %} #}
                    </button>
                    {% if courseManager.canAccessToModules(participation, app.user) %}
                        <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium submit-module-admin {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="admin">
                            {% if editable %}
                                {{ "front.btn.validate_admin"|trans }}
                            {% else %}
                                {{ "front.btn.redirect_admin"|trans }}
                            {% endif %}
                        </button>
                    {% endif %}
                </div>
                {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
                {% if modulesLeftSentence %}
                    <div class="text-right font-16">
                    {{ modulesLeftSentence|raw }}
                    </div>
                {% endif %}
            </div>
            {{ form_end(form) }}
        </div>
    </div>

{% endblock %}



