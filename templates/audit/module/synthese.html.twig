{% extends 'audit/module/base.html.twig' %}

{% block module_body %}
    {% if participant is defined %}
        {% if participant is not null %}
            <b>
                {{ participant.lastname }} {{ participant.firstname }}<br>
                {% if participant.rpps is not null %} RPPS : {{ participant.rpps }} <br>{% endif %}
                {% if participant.adeli is not null %} ADELI : {{ participant.adeli }} <br>{% endif %}
            </b><br>
        {% endif %}
    {% else %}
        <div class="alert alert-danger alert-obligatoire module-notif">Ce module est obligatoire.</div>
    {% endif %}
    <div class="page-content">

        <p class="text-big">
            Parmi les objectifs de la formation listés ci-dessous, merci d'indiquer votre positionnement suite à la formation que vous avez suivie.
            <br><br>
            Ce questionnaire est anonyme.
        </p>

        <div>
            {{ form_start(form) }}
            {% if formation.programme.connaissances|length %}
                <div class="etutorat-rows-title etutorat-rows-title-synthese">
                    <p class="bold mbn"><span class="underline">Objectifs spécifiques de la formation continue :</span> Connaissances attendues</p>
                    <div class="etutorat-notes">
                        <span>Acquis</span>
                        <span>En cours d'acquisition</span>
                        <span>Non acquis</span>
                    </div>
                </div>
                <div class="etutorat-rows">
                    {% for key, child in form.children %}
                        {% if "connaissances_" in key  %}
                            <div class="etutorat-row">
                                <div class="etutorat-row-label">
                                    {{ form_label(form[key]) }}
                                </div>
                                <div class="etutorat-row-answers etutorat-row-answers--showInResponsive etutorat-row-answers-synthese">
                                    {{ form_widget(form[key]) }}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
            {% if formation.programme.competences|length %}
                <div class="etutorat-rows-title etutorat-rows-title-synthese">
                    <p class="bold mbn"><span class="underline">Objectifs spécifiques de la formation continue :</span> Compétences attendues</p>
                    <div class="etutorat-notes">
                        <span>Acquis</span>
                        <span>En cours d'acquisition</span>
                        <span>Non acquis</span>
                    </div>
                </div>
                <div class="etutorat-rows">
                    {% for key, child in form.children %}
                        {% if "competence_" in key  %}
                            <div class="etutorat-row">
                                <div class="etutorat-row-label">
                                    {{ form_label(form[key]) }}
                                </div>
                                <div class="etutorat-row-answers etutorat-row-answers--showInResponsive etutorat-row-answers-synthese">
                                    {{ form_widget(form[key]) }}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}

            {% if participant is not defined %}
                {% if participation.id %}
                    <div class="page-footer">
                        {% if validationDisabled %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
                        <div class="text-right">
                            <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="next">
                                Suivant <i class="fa-solid fa-chevron-right next-icon"></i>
                                {# {% if canEdit %}
                                    Enregistrer et passer à la page suivante
                                {% else %}
                                    Passer au module suivant
                                {% endif %} #}
                            </button>
                            {% if courseManager.canAccessToModules(participation, app.user) %}
                                <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="admin">
                                    {% if canEdit %}
                                        {{ "front.btn.validate_admin"|trans }}
                                    {% else %}
                                        {{ "front.btn.redirect_admin"|trans }}
                                    {% endif %}
                                </button>
                            {% endif %}
                        </div>
                        {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
                        {% if modulesLeftSentence %}
                            <div class="text-right font-16">
                            {{ modulesLeftSentence|raw }}
                            </div>
                        {% endif %}
                    </div>

                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endblock %}



