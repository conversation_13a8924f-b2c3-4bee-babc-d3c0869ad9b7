{% extends 'audit/module/base.html.twig' %}

{% block module_css %}
{% endblock %}

{% block module_body %}
    <div class="page-content">

        <p class="text-big mtl mbl">L’objectif de ce programme étant d’améliorer sa pratique professionnelle quotidienne,
            merci de compléter la fiche action ci-dessous qui vous
            permet de noter et de suivre votre (ou vos) action(s) d’amélioration.</p>

        <table class="table-action rwd-table">
            <tr>
                <th style="width: 33%">Problématique identifiée</th>
                <th style="width: 33%">Action d'amélioration entreprise</th>
                {% if canEdit %}
                    <th style="width: 33%">Actions</th>
                {% endif %}
            </tr>
            {% for ficheAction in participation.ficheActions %}
            <tr class="action-row" data-edited="true">
                <td data-th="Problématique identifiée" class="edit-field" data-field="problematique">{{ ficheAction.problematique }}</td>
                <td data-th="Action d'amélioration entreprise" class="edit-field" data-field="action">{{ ficheAction.action }}</td>
                {% if canEdit %}
                    <td data-th="Actions">
                        <a class="btn btn-eduprat btn-icon edit-action" style="display:none" href="#!" title="{{ 'admin.global.update'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                        <a class="btn btn-eduprat btn-icon delete-action" style="margin-bottom: 4px;" href="#!" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                    </td>
                {% endif %}
            </tr>
            {% endfor %}
            {% if canEdit %}
                <tr class="add-action-row">
                    <td colspan="3" class="text-center text-bold">
                        <a id="add-action" href="#!"><i class="fa fa-plus"></i>&nbsp&nbspAjouter une problématique</a>
                    </td>
                </tr>
            {% endif %}
        </table>

        {% if canEdit %}
            <p class="mtl mbl text-big">
                {% if formation.isDefaultType %}
                    Suite aux actions d’amélioration que vous avez définies, vous avez jusqu’au {{ formation.closingDate|date("d/m/Y") }} pour compléter votre audit 2 sur la base de 10 nouveaux dossiers patients
                {% elseif formation.isVignette %}
                    Suite aux actions d’amélioration que vous avez définies, vous avez jusqu’au {{ formation.closingDate|date("d/m/Y") }} pour poursuivre votre formation
                {% endif %}
            </p>
        {% endif %}

        {% if formation.isFormPost28d and not formation.isThirdUnityOpen %}
            <div class="alert alert-message alert-warning">
                <div class="icon">
                    <i class="fa fa-lightbulb-o" aria-hidden="true"></i>
                </div>
                <div class="text text-bold">
                    Vous avez maintenant 4 semaines pour sélectionner 5 à 10 dossiers patients reçus en cabinet sur la thématique {{ formation.programme.title }}.<br>
                    La saisie de la grille d’audit sera accessible à partir du {{ formation.thirdUnityOpeningDate|date("d/m/Y") }}
                </div>
            </div>
        {% endif %}

        <br><br><br>
        <div>
            {% if form is not null %}
                {{ form_start(form) }}
                <div class="hidden">
                    {{ form_rest(form) }}
                </div>
                <div class="page-footer">
                    {% if validationDisabled %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
                    <div class="text-right">
                        <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="next">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></button>
                        {% if courseManager.canAccessToModules(participation, app.user) %}
                            <button {% if validationDisabled %} disabled {% endif %} type="submit" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}" name="redirect" value="admin">{{ "front.btn.validate_admin"|trans }}</button>
                        {% endif %}
                    </div>
                    {% if not courseManager.canAccessToModules(participation, app.user) %}
                        <div class="text-right mts">
                            <a {% if validationDisabled %} disabled {% endif %} href="#!" class="link-sub-btn link-disabled {% if validationDisabled %} validationDisabled disabled {% endif %}">Je poursuis ma formation plus tard, au plus tard le {{ formation.closingDate|date("d/m/Y") }}</a>
                        </div>
                    {% endif %}
                </div>
                {{ form_end(form) }}
            {% else %}
                <div class="page-footer">
                    <div class="text-right">
                        <a href="{{ url("eduprat_front_next_module", { participation: participation.id, module: current_module }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
                        {% if courseManager.canAccessToModules(participation, app.user) %}
                            <a href="{{ url("eduprat_front_next_module", { participation: participation.id, module: current_module, redirect: "admin" }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled {% endif %}">{{ "front.btn.redirect_admin"|trans }}</a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
            {% set isLastModuleInStep = courseManager.isLastModuleInStep(current_module, step) %}
            {% if isLastModuleInStep and formation.isThreeUnity() and not formation.isThirdUnityOpen() %}
                <div class="text-right">
                    Le prochain module sera accessible le <b>{{ formation.thirdUnityOpeningDate()|date("d/m/Y") }}</b>
                </div>
            {% endif %}
            {% if formation.isThirdUnityOpen() %}
                {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
                {% if modulesLeftSentence %}
                    <div class="text-right font-16">
                    {{ modulesLeftSentence|raw }}
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>

    <template id="action-row-template">
        <tr class="action-row" data-edited="false">
            <td data-th="Problématique identifiée" class="edit-field" data-field="problematique" placeholder="Cliquez ici pour compléter"></td>
            <td data-th="Action d'amélioration entreprise" class="edit-field" data-field="action" placeholder="Cliquez ici pour compléter"></td>
            <td data-th="Actions">
                <a class="btn btn-eduprat btn-icon edit-action" style="display:none" href="#!" title="{{ 'admin.global.update'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                <a class="btn btn-eduprat btn-icon delete-action" style="margin-bottom: 4px;" href="#!" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
            </td>
        </tr>
    </template>

{% endblock %}

{% block module_javascripts %}
    {% include "audit/module/fiche_action_js.html.twig" %}
{% endblock %}



