{% extends 'audit/module/base.html.twig' %}

{% block module_css %}
    <link href="{{ asset('css/commun_pdf_front.css') }}" rel="stylesheet">
    <link href="{{ asset('css/pdf/restitution_audit_groupe_individuelle.css') }}" rel="stylesheet">
    <link href="{{ asset('css/pdf_front.css') }}" rel="stylesheet">
{% endblock %}

{% block module_body %}
    <div class="alert alert-danger alert-obligatoire module-notif">Ce module est obligatoire.</div>
    {% set summaryIndex = 0 %}

    <div class="page-content">

        <div class="text-big">
            {% if formation.isVfc %}
                Vous trouverez ci-dessous les réponses aux vignettes cliniques.
            {% else %}
                 Vous trouverez ci-dessous votre  {% if participation.formation.isTcs %} score de {% else %} résultat {% endif %} {{ ("front.label.formType." ~ participation.formation.formTypePre ~ ".a") | trans }} {% if formation.hasFormPost %}pré-formation{% endif %} {% if participation.formation.isTcs %} avec le panel d'experts {% endif %}et une comparaison avec les autres participants de la session.
            {% endif %}
        </div>

        <p class="text-big">Nombre de répondants : {{ respondents }}</p>

        {% if participation.formation.isTcs %}
             <p class="text-big">Votre score global de concordance est de {{ tcsMoyenne }}% par rapport au panel d'expert</p>
        {% endif %}

        {% if (formation.isFormPredefined or formation.isFormPreVignette) and not formation.isVfc %}
            <p class="text-big" style="font-size: 18px">Vous avez <span class="bold">{{ globalScores[1]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePre ~ ".plural") | trans }}{% if formation.hasFormPost %}pré formation{% endif %}.</p>
        {% endif %}

        <div class="nav-tabs-front mtl">
            <ul class="nav nav-tabs">
                {% if not formation.isVfc %}
                    <li class="active"><a href="#tab_theme" data-toggle="tab" aria-expanded="true">{% if participation.formation.isTcs %} Score de concordance global {% else %}Score moyen par thème{% endif %}</a></li>
                    <li><a href="#tab_indicator" data-toggle="tab" aria-expanded="false">{% if participation.formation.isTcs %}Score de concordance par domaine{% else %}Analyse par indicateur{% endif %}</a></li>
                {% endif %}
                {% if formation.isFormPreVignette or formation.isTcs %}
                    <li {% if formation.isVfc %}class="active"{% endif %}><a href="#tab_analyse" data-toggle="tab"  {% if formation.isVfc %} aria-expanded="true" {% else %} aria-expanded="false" {% endif %}>{% if participation.formation.isTcs %}Analyse par item{% else %}Analyse par question{% endif %}</a></li>
                {% endif %}
                {% if formation.isFormPreDefault %}
                    <li><a href="#tab_criteres" data-toggle="tab" aria-expanded="false">Nombre de bonnes réponses par question</a></li>
                {% endif %}
                {% if (formation.isFormPreDefault or formation.isFormPreVignette or formation.isTcs) and not formation.isVfc %}
                    <li><a href="#tab_axes" data-toggle="tab" aria-expanded="false">{{ "restitution.synthese" | trans }}</a></li>
                {% endif %}
            </ul>
            <div class="tab-content tab-eduprat">
                {% if not formation.isVfc %}
                    <div class="tab-pane active" id="tab_theme">
                        <div id="spiderweb" data-width="auto"></div>
                    </div>
                    <div class="tab-pane" id="tab_indicator">
                        {% include "pdf/section/indicators.html.twig" with {'show2': false, 'showAvg2': false, showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isFormPreVignette %}
                    <div class="tab-pane {% if formation.isVfc %} active{% endif %}" id="tab_analyse">
                        {% include "pdf/section/analyse.html.twig" with { auditId: 1, showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isTcs %}
                    <div class="tab-pane" id="tab_analyse">
                        {% include "pdf/section/analyseTcs.html.twig" with { auditId: 1, showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isFormPreDefault %}
                    <div class="tab-pane" id="tab_criteres">
                        {% include "pdf/section/criteres.html.twig" with { showTitle: false } %}
                    </div>
                {% endif %}
                {% if (formation.isFormPreDefault or formation.isFormPreVignette) and not formation.isVfc %}
                    <div class="tab-pane" id="tab_axes">
                        {% include "pdf/section/axes.html.twig" with { auditId: 1, showTitle: false } %}
                    </div>
                {% endif %}
                {% if formation.isTcs %}
                    <div class="tab-pane" id="tab_axes">
                        {% include "pdf/section/axesTcs.html.twig" with { auditId: 1, showTitle: false } %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="page-footer">
        {% if validationDisabled %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
        <div class="text-right" >
            <a id="download-file" data-type="prerestitution" data-participation="{{ participation.id }}" target="_blank" href="{{ url("pdf_audit_restitution_groupe_individuelle_pdf", { id: participation.id, oneCoordinator: true, token: participation.token }) }}" class="btn btn-eduprat btn-medium download-file btn-secondaire">Télécharger le PDF</a>
            <div class="mll" style="display: inline-block">
                <a id="validate-step" href="{{ url("eduprat_front_formation_prerestitution_validate", { id: participation.id }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled{% endif %}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
            </div>
            {% if courseManager.canAccessToModules(participation, app.user) %}
                <div class="mll" style="display: inline-block">
                    <a href="{{ url("eduprat_front_formation_prerestitution_validate", { id: participation.id, redirect: "admin" }) }}" class="btn btn-eduprat btn-medium {% if validationDisabled %} validationDisabled disabled{% endif %}">{{ "front.btn.validate_admin"|trans }}</a>
                </div>
            {% endif %}
        </div>
        {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
        {% if modulesLeftSentence %}
            <div class="text-right font-16">
            {{ modulesLeftSentence|raw }}
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block module_javascripts %}
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/highcharts-more.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
    <script src="{{ asset('js/radarChart.js') }}"></script>
    {% include "pdf/script/prerestitution.html.twig" %}
    <script>

	    $(function () {
		    tippy('#download-tooltip', {
			    content: "Veuillez télécharger la pré-restitution pour valider le module",
		    });
	    })

        document.getElementById("download-file").onclick = function() {
	        let link = document.getElementById("validate-step");
	        link.classList.remove("disabled");
	        if (document.getElementById("validate-step").dataset.href) {
		        document.getElementById("validate-step").href = document.getElementById("validate-step").dataset.href;
            }
        }
    </script>
{% endblock %}