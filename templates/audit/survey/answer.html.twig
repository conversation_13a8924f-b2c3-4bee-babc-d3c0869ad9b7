{% extends 'audit/base.html.twig' %}

{% block css %}
    <link rel="stylesheet" href="{{ asset('css/audit.css') }}">
    <link rel="stylesheet" href="{{ asset('js/Gallery-2.33.0/css/blueimp-gallery.min.css') }}">
    <link href="{{ asset('css/foundation-components/spaces.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    {% set auditId = surveyId %}
    {% include "audit/common/timeline.html.twig" with { welcome: true } %}
    <div class="alert alert-danger alert-obligatoire module-notif">Ce module est obligatoire.</div>

    <div>
        <a class="pull-right" href="{{ url('pdf_survey_pdf', {'id': participation.id, 'surveyId': surveyId, 'token': participation.token }) }}" target="_blank" title="{{ 'admin.formation.telecharger'|trans }}">
            <svg style="fill: #999999" viewBox="0 0 100 100" height="26">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset("img/course/importer.svg") }}#icon"></use>
            </svg>
        </a>
    </div>

    <div id="form">
        {{ form_start(form) }}
        {% include "audit/common/form_images.html.twig" %}
        {{ form_rest(form) }}
        <div class="page-footer">
            <div class="text-right">
                <button {% if validationDisabled %} disabled {% endif %} id="valid-audit" class="btn btn-big btn-eduprat valid-alert {% if validationDisabled %} validationDisabled disabled {% endif %}" type="submit" name="redirect" value="next">
                    Suivant <i class="fa-solid fa-chevron-right next-icon"></i>
                    {# Je valide {{ ('front.label.' ~ participation.formation.displayType ~ '.mon')|trans|lower }} #}
                </button>
                {% if courseManager.canAccessToModules(participation, app.user) %}
                    <button {% if validationDisabled %} disabled {% endif %} class="btn btn-big btn-eduprat valid-alert {% if validationDisabled %} validationDisabled disabled {% endif %}" type="submit" name="redirect" value="admin" >
                        {{ "front.btn.validate_admin"|trans }}
                    </button>
                {% endif %}
            </div>
            <div class="text-right">
                {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
                {% if modulesLeftSentence %}
                    <div class="text-right font-16">
                    {{ modulesLeftSentence|raw }}
                    </div>
                {% endif %}
            </div>
        </div>
        {{ form_end(form) }}

        {% for key, picture in choicePictures %}
            <img style="max-width: 100%;" id="{{ key }}" class="choicePicture" src="{{ picture }}" alt="">
        {% endfor %}
    </div>
{% endblock %}

{% block javascripts %}
    <script src="{{ asset('js/Gallery-2.33.0/js/blueimp-gallery.min.js') }}"></script>
    <script src="{{ asset('js/gallery.js') }}"></script>
    <script>
	    jQuery(document).ready(function () {
            $("input[type=checkbox]").click(function(){
                if($(this).is(":checked") && $(this).attr('value') == "ne_saispas") {
                    var name = $(this).attr('name');
                    $('input[name="'+name+'"]').each(function( index ) {
                        if ($(this).attr('value') != "ne_saispas") {
                            $(this).prop("checked", false);
                        }
                    });
                } else if($(this).is(":checked") && $(this).attr('value') != "ne_saispas") {
                    var name = $(this).attr('name');
                    $('input[name="'+name+'"]').each(function( index ) {
                        if ($(this).attr('value') == "ne_saispas") {
                            $(this).prop("checked", false);
                        }
                    });
                }
            });
		    redirectIfInactive(120 * 60 * 1000)

            $('.choicePicture').each(function() {
                var id = $(this).attr('id');
                var choice = $('*[data-id='+id+']*')[0];
                var choiceId = choice["id"];
                var choiceParent = $('#'+choiceId).parent();
                choiceParent.append('<div style="height:10px"></div>');
                $(this).appendTo(choiceParent);
                choiceParent.append('<div style="height:10px"></div>');
            });

            $('.slide-content').each(function() {
                $(this).click(function() {
                    if ($(this).hasClass('slide-content-big')) {
                        $(this).removeClass('slide-content-big');
                    } else {
                        $(this).addClass('slide-content-big');
                    }
                });
            });

	    });
    </script>
{% endblock %}
