<?php

// Script pour analyser les routes par rôle
$controllers = [
    'src/Eduprat/AdminBundle/Controller',
    'src/Eduprat/AuditBundle/Controller', 
    'src/Eduprat/ApiBundle/Controller',
    'src/Eduprat/CrmBundle/Controller',
    'src/Eduprat/PdfBundle/Controller',
    'src/Eduprat/DomainBundle/Controller'
];

$routes = [];

function extractRouteInfo($file) {
    $content = file_get_contents($file);
    $routes = [];
    
    // Extraire les annotations de classe
    preg_match('/class\s+(\w+).*?{/s', $content, $classMatch);
    $className = $classMatch[1] ?? 'Unknown';
    
    // Extraire les annotations de sécurité au niveau de la classe
    preg_match('/#\[IsGranted\([\'"]([^\'"]+)[\'"]\)\]\s*class/s', $content, $classSecurityMatch);
    $classRole = $classSecurityMatch[1] ?? null;
    
    // Extraire le préfixe de route de la classe
    preg_match('/#\[Route\(path:\s*[\'"]([^\'"]+)[\'"]\)\]\s*(?:#\[IsGranted.*?\]\s*)?class/s', $content, $classRouteMatch);
    $classPrefix = $classRouteMatch[1] ?? '';
    
    // Extraire toutes les méthodes avec leurs routes et méthodes HTTP
    preg_match_all('/#\[Route\((.*?)\)\]\s*(?:#\[IsGranted\([\'"]([^\'"]+)[\'"]\)\]\s*)?public\s+function\s+(\w+)/s', $content, $matches, PREG_SET_ORDER);
    
    foreach ($matches as $match) {
        $routeParams = $match[1];
        $methodRole = $match[2] ?? null;
        $methodName = $match[3];
        
        // Extraire les paramètres de la route
        preg_match('/path:\s*[\'"]([^\'"]+)[\'"]/', $routeParams, $pathMatch);
        preg_match('/name:\s*[\'"]([^\'"]+)[\'"]/', $routeParams, $nameMatch);
        preg_match('/methods:\s*\[([^\]]+)\]/', $routeParams, $methodsMatch);
        
        $path = $pathMatch[1] ?? '';
        $routeName = $nameMatch[1] ?? 'unknown_route';
        $httpMethods = $methodsMatch[1] ?? 'ANY';
        
        // Nettoyer les méthodes HTTP
        $httpMethods = str_replace(['"', "'", ' '], '', $httpMethods);
        
        $fullPath = $classPrefix . $path;
        $role = $methodRole ?: $classRole;
        
        $routes[] = [
            'name' => $routeName,
            'path' => $fullPath,
            'methods' => $httpMethods,
            'role' => $role ?: 'PUBLIC',
            'controller' => $className,
            'method' => $methodName,
            'file' => basename($file)
        ];
    }
    
    return $routes;
}

// Parcourir tous les contrôleurs
foreach ($controllers as $dir) {
    if (is_dir($dir)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($iterator as $file) {
            if ($file->getExtension() === 'php' && strpos($file->getFilename(), 'Controller.php') !== false) {
                $fileRoutes = extractRouteInfo($file->getPathname());
                $routes = array_merge($routes, $fileRoutes);
            }
        }
    }
}

// Grouper par rôle
$routesByRole = [];
foreach ($routes as $route) {
    $role = $route['role'];
    if (!isset($routesByRole[$role])) {
        $routesByRole[$role] = [];
    }
    $routesByRole[$role][] = $route;
}

// Trier les rôles par ordre hiérarchique
$roleOrder = [
    'ROLE_SUPER_ADMIN',
    'ROLE_WEBMASTER_COMPTA', 
    'ROLE_WEBMASTER',
    'ROLE_SUPERVISOR_FRANCE',
    'ROLE_SUPERVISOR',
    'ROLE_COORDINATOR',
    'ROLE_COORDINATOR_LBI',
    'ROLE_FORMER',
    'PUBLIC'
];

echo "# LISTE DÉTAILLÉE DES ROUTES PAR RÔLE - EDUPRAT\n\n";

foreach ($roleOrder as $role) {
    if (!isset($routesByRole[$role])) continue;
    
    echo "## 🔐 " . $role . " (" . count($routesByRole[$role]) . " routes)\n\n";
    
    // Trier les routes par nom
    usort($routesByRole[$role], function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });
    
    foreach ($routesByRole[$role] as $route) {
        echo "### " . $route['name'] . "\n";
        echo "- **Chemin:** `" . $route['path'] . "`\n";
        echo "- **Méthodes:** " . $route['methods'] . "\n";
        echo "- **Contrôleur:** " . $route['controller'] . "::" . $route['method'] . "\n";
        echo "- **Fichier:** " . $route['file'] . "\n\n";
    }
    
    echo "---\n\n";
}

// Afficher les autres rôles non listés
foreach ($routesByRole as $role => $routeList) {
    if (!in_array($role, $roleOrder)) {
        echo "## 🔐 " . $role . " (" . count($routeList) . " routes)\n\n";
        
        usort($routeList, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        foreach ($routeList as $route) {
            echo "### " . $route['name'] . "\n";
            echo "- **Chemin:** `" . $route['path'] . "`\n";
            echo "- **Méthodes:** " . $route['methods'] . "\n";
            echo "- **Contrôleur:** " . $route['controller'] . "::" . $route['method'] . "\n";
            echo "- **Fichier:** " . $route['file'] . "\n\n";
        }
        
        echo "---\n\n";
    }
}

?>
