LISTE DÉTAILLÉE DES ROUTES ET RÔLES D'ACCÈS - EDUPRAT
====================================================

🔗 **admin_attestation_delete_file**
   📍 Chemin: /session/{id}/attestation-delete-file/{n1}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::attestationDeleteFileDownload
   📄 Fichier: SessionController.php

🔗 **admin_attestation_file**
   📍 Chemin: /session/{id}/attestation-file/{n1}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::attestationFileDownload
   📄 Fichier: SessionController.php

🔗 **admin_audit_archive**
   📍 Chemin: /audit/{id}/archive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::archive
   📄 Fichier: AuditController.php

🔗 **admin_audit_category_create**
   📍 Chemin: /audit-category/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditCategoryController::create
   📄 Fichier: AuditCategoryController.php

🔗 **admin_audit_category_delete**
   📍 Chemin: /audit-category/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditCategoryController::delete
   📄 Fichier: AuditCategoryController.php

🔗 **admin_audit_category_edit**
   📍 Chemin: /audit-category/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditCategoryController::edit
   📄 Fichier: AuditCategoryController.php

🔗 **admin_audit_category_index**
   📍 Chemin: /audit-category/audit-category
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditCategoryController::index
   📄 Fichier: AuditCategoryController.php

🔗 **admin_audit_choice_type**
   📍 Chemin: /audit/choice_type
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::choiceType
   📄 Fichier: AuditController.php

🔗 **admin_audit_create**
   📍 Chemin: /audit/create/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::create
   📄 Fichier: AuditController.php

🔗 **admin_audit_delete**
   📍 Chemin: /audit/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::delete
   📄 Fichier: AuditController.php

🔗 **admin_audit_dofilter**
   📍 Chemin: /audit/filter/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::doFilter
   📄 Fichier: AuditController.php

🔗 **admin_audit_duplicate**
   📍 Chemin: /audit/{id}/duplicate
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::duplicate
   📄 Fichier: AuditController.php

🔗 **admin_audit_edit**
   📍 Chemin: /audit/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::edit
   📄 Fichier: AuditController.php

🔗 **admin_audit_edit_predefined**
   📍 Chemin: /audit/{id}/edit-predefined/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::editPredefined
   📄 Fichier: AuditController.php

🔗 **admin_audit_edit_predefined_add**
   📍 Chemin: /audit/{id}/edit-predefined-add
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::editPredefinedAdd
   📄 Fichier: AuditController.php

🔗 **admin_audit_edit_predefined_delete**
   📍 Chemin: /audit/{id}/edit-predefined-delete/{patient}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::editPredefinedDelete
   📄 Fichier: AuditController.php

🔗 **admin_audit_edit_predefined_move**
   📍 Chemin: /audit/edit-predefined-move
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::editPredefinedMove
   📄 Fichier: AuditController.php

🔗 **admin_audit_index**
   📍 Chemin: /audit/audit
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::index
   📄 Fichier: AuditController.php

🔗 **admin_audit_question_category_create**
   📍 Chemin: /audit-question-category/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditQuestionCategoryController::create
   📄 Fichier: AuditQuestionCategoryController.php

🔗 **admin_audit_question_category_delete**
   📍 Chemin: /audit-question-category/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditQuestionCategoryController::delete
   📄 Fichier: AuditQuestionCategoryController.php

🔗 **admin_audit_question_category_edit**
   📍 Chemin: /audit-question-category/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditQuestionCategoryController::edit
   📄 Fichier: AuditQuestionCategoryController.php

🔗 **admin_audit_question_category_index**
   📍 Chemin: /audit-question-category/audit-question-category
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditQuestionCategoryController::index
   📄 Fichier: AuditQuestionCategoryController.php

🔗 **admin_audit_unarchive**
   📍 Chemin: /audit/{id}/unarchive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: AuditController::unarchive
   📄 Fichier: AuditController.php

🔗 **admin_classeur_bilan**
   📍 Chemin: /classeur-bilan/{year}/{quarter}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::classeurBilan
   📄 Fichier: DefaultController.php

🔗 **admin_compensation_create**
   📍 Chemin: /indemnisation/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: CompensationController::create
   📄 Fichier: CompensationController.php

🔗 **admin_compensation_delete**
   📍 Chemin: /indemnisation/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: CompensationController::delete
   📄 Fichier: CompensationController.php

🔗 **admin_compensation_edit**
   📍 Chemin: /indemnisation/edit/{id}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: CompensationController::edit
   📄 Fichier: CompensationController.php

🔗 **admin_compensation_index**
   📍 Chemin: /indemnisation/indemnisation
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: CompensationController::index
   📄 Fichier: CompensationController.php

🔗 **admin_comptabilite_index**
   📍 Chemin: /comptabilite/comptabilite
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ComptabiliteController::index
   📄 Fichier: ComptabiliteController.php

🔗 **admin_config_emails**
   📍 Chemin: /config/config
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ConfigController::emails
   📄 Fichier: ConfigController.php

🔗 **admin_config_emails_send**
   📍 Chemin: /config/emails/send/{alias}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ConfigController::emailsSend
   📄 Fichier: ConfigController.php

🔗 **admin_config_emails_send_participation**
   📍 Chemin: /config/emails/send/{alias}/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ConfigController::emailsSendParticipation
   📄 Fichier: ConfigController.php

🔗 **admin_config_emails_sendall**
   📍 Chemin: /config/emails/send-all
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ConfigController::emailsSendAll
   📄 Fichier: ConfigController.php

🔗 **admin_convention_file**
   📍 Chemin: /convention_file/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FilesController::conventionFileDownload
   📄 Fichier: FilesController.php

🔗 **admin_coordinator_dashboard**
   📍 Chemin: /user/coordinator-dashboard/{id}/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: UserController::coordinatorDashboard
   📄 Fichier: UserController.php

🔗 **admin_coordinator_file**
   📍 Chemin: /coordinator_files/coordinator_files
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: CoordinatorFilesController::coordinatorFileDownload
   📄 Fichier: CoordinatorFilesController.php

🔗 **admin_coordinator_table**
   📍 Chemin: /user/coordinator-table/{id}/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: UserController::coordinatorTable
   📄 Fichier: UserController.php

🔗 **admin_crm_comptabilite**
   📍 Chemin: /comptabilite/comptabilite
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ComptabiliteController::suivi
   📄 Fichier: ComptabiliteController.php

🔗 **admin_crm_comptabilite_attestation_files**
   📍 Chemin: /comptabilite/attestations/{year}/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ComptabiliteController::AttestationFiles
   📄 Fichier: ComptabiliteController.php

🔗 **admin_crm_comptabilite_files**
   📍 Chemin: /comptabilite/documents/{year}/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ComptabiliteController::files
   📄 Fichier: ComptabiliteController.php

🔗 **admin_crm_comptabilite_inscriptions**
   📍 Chemin: /comptabilite/inscriptions/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ComptabiliteController::inscriptions
   📄 Fichier: ComptabiliteController.php

🔗 **admin_dashboard_csv**
   📍 Chemin: /dashboard-csv/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: DefaultController::csvDashboard
   📄 Fichier: DefaultController.php

🔗 **admin_dashboard_show**
   📍 Chemin: /dashboard/dashboard
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DashboardController::dashboard
   📄 Fichier: DashboardController.php

🔗 **admin_document_pedagogique_file**
   📍 Chemin: /topo_files/{id}/fileDocumentsPedagogique/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: TopoFilesController::documentsPedagogiquesFileDownload
   📄 Fichier: TopoFilesController.php

🔗 **admin_download_models**
   📍 Chemin: /download-models
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::downloadModels
   📄 Fichier: DefaultController.php

🔗 **admin_elearning_archive**
   📍 Chemin: /elearning/{id}/archive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::archive
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_choice_type**
   📍 Chemin: /elearning/choice_type
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::choiceType
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_create**
   📍 Chemin: /elearning/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::create
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_delete**
   📍 Chemin: /elearning/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::delete
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_dofilter**
   📍 Chemin: /elearning/filter/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::doFilter
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_duplicate**
   📍 Chemin: /elearning/{id}/duplicate
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::duplicateElearning
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_edit**
   📍 Chemin: /elearning/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::edit
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_edit_predefined**
   📍 Chemin: /elearning/{id}/edit-predefined/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::editPredefined
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_edit_predefined_add**
   📍 Chemin: /elearning/{id}/edit-predefined-add
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::editPredefinedAdd
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_edit_predefined_delete**
   📍 Chemin: /elearning/{id}/edit-predefined-delete/{patient}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::editPredefinedDelete
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_edit_predefined_move**
   📍 Chemin: /elearning/edit-predefined-move
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::editPredefinedMove
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_index**
   📍 Chemin: /elearning/elearning
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::index
   📄 Fichier: ElearningController.php

🔗 **admin_elearning_unarchive**
   📍 Chemin: /elearning/{id}/unarchive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::unarchive
   📄 Fichier: ElearningController.php

🔗 **admin_emargement_file**
   📍 Chemin: /emargement_file/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FilesController::emargementFileDownload
   📄 Fichier: FilesController.php

🔗 **admin_faq_create**
   📍 Chemin: /faq/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FAQController::create
   📄 Fichier: FAQController.php

🔗 **admin_faq_delete**
   📍 Chemin: /faq/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FAQController::delete
   📄 Fichier: FAQController.php

🔗 **admin_faq_edit**
   📍 Chemin: /faq/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FAQController::edit
   📄 Fichier: FAQController.php

🔗 **admin_faq_index**
   📍 Chemin: /faq/faq
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FAQController::index
   📄 Fichier: FAQController.php

🔗 **admin_faq_position**
   📍 Chemin: /faq/position
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FAQController::position
   📄 Fichier: FAQController.php

🔗 **admin_finance_mode_children**
   📍 Chemin: /finance-mode/{id}/children
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceModeController::children
   📄 Fichier: FinanceModeController.php

🔗 **admin_finance_mode_create**
   📍 Chemin: /finance-mode/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceModeController::create
   📄 Fichier: FinanceModeController.php

🔗 **admin_finance_mode_delete**
   📍 Chemin: /finance-mode/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceModeController::delete
   📄 Fichier: FinanceModeController.php

🔗 **admin_finance_mode_edit**
   📍 Chemin: /finance-mode/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceModeController::edit
   📄 Fichier: FinanceModeController.php

🔗 **admin_finance_mode_index**
   📍 Chemin: /finance-mode/finance-mode
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceModeController::index
   📄 Fichier: FinanceModeController.php

🔗 **admin_finance_sous_mode_create**
   📍 Chemin: /finance-sous-mode/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceSousModeController::create
   📄 Fichier: FinanceSousModeController.php

🔗 **admin_finance_sous_mode_delete**
   📍 Chemin: /finance-sous-mode/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceSousModeController::delete
   📄 Fichier: FinanceSousModeController.php

🔗 **admin_finance_sous_mode_dofilter**
   📍 Chemin: /finance-sous-mode/filter/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceSousModeController::doFilter
   📄 Fichier: FinanceSousModeController.php

🔗 **admin_finance_sous_mode_edit**
   📍 Chemin: /finance-sous-mode/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceSousModeController::edit
   📄 Fichier: FinanceSousModeController.php

🔗 **admin_finance_sous_mode_index**
   📍 Chemin: /finance-sous-mode/finance-sous-mode
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FinanceSousModeController::index
   📄 Fichier: FinanceSousModeController.php

🔗 **admin_formateur_file**
   📍 Chemin: /formateur_files/formateur_files
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormateurFilesController::formateurFileDownload
   📄 Fichier: FormateurFilesController.php

🔗 **admin_formation_account**
   📍 Chemin: /session/{id}/account/{mid}/{state}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER_COMPTA
   📁 Contrôleur: SessionController::account
   📄 Fichier: SessionController.php

🔗 **admin_formation_bill**
   📍 Chemin: /session/{id}/bill/{state}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::bill
   📄 Fichier: SessionController.php

🔗 **admin_formation_choice_type**
   📍 Chemin: /session/choice_type/{programme}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::choiceType
   📄 Fichier: SessionController.php

🔗 **admin_formation_choice_type_elearning**
   📍 Chemin: /session/choice_type_elearning/{programme}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::choiceTypeElearning
   📄 Fichier: SessionController.php

🔗 **admin_formation_close_andpc_formation**
   📍 Chemin: /session/{id}/close-andpc
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::closeAndpc
   📄 Fichier: SessionController.php

🔗 **admin_formation_close_formation**
   📍 Chemin: /session/{id}/close
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::close
   📄 Fichier: SessionController.php

🔗 **admin_formation_create**
   📍 Chemin: /session/create/{programme}/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::create
   📄 Fichier: SessionController.php

🔗 **admin_formation_delete**
   📍 Chemin: /session/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::delete
   📄 Fichier: SessionController.php

🔗 **admin_formation_duplicate_check**
   📍 Chemin: /session/duplicate-check
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::preventDuplicate
   📄 Fichier: SessionController.php

🔗 **admin_formation_edit**
   📍 Chemin: /session/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::edit
   📄 Fichier: SessionController.php

🔗 **admin_formation_email**
   📍 Chemin: /session/{id}/email/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::sendEmail
   📄 Fichier: SessionController.php

🔗 **admin_formation_export_emails**
   📍 Chemin: /session/{id}/export-email-history
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::exportMailerHistory
   📄 Fichier: SessionController.php

🔗 **admin_formation_export_formation**
   📍 Chemin: /session/{id}/export
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::export
   📄 Fichier: SessionController.php

🔗 **admin_formation_export_participation_logs**
   📍 Chemin: /session/{id}/export-participations-logs
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::exportParticipationLogs
   📄 Fichier: SessionController.php

🔗 **admin_formation_export_participation_logs_unity**
   📍 Chemin: /session/{id}/export-participations-logs/{unity}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::exportParticipationLogsByUnity
   📄 Fichier: SessionController.php

🔗 **admin_formation_export_participations_histories**
   📍 Chemin: /participant/{id}/export-participations-histories
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipantController::exportParticipationHistories
   📄 Fichier: ParticipantController.php

🔗 **admin_formation_file**
   📍 Chemin: /session/{id}/file/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::fileDownload
   📄 Fichier: SessionController.php

🔗 **admin_formation_import_formation**
   📍 Chemin: /session/{id}/import
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::import
   📄 Fichier: SessionController.php

🔗 **admin_formation_import_zoom**
   📍 Chemin: /session/{id}/import-zoom
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SessionController::importZoom
   📄 Fichier: SessionController.php

🔗 **admin_formation_index**
   📍 Chemin: /session/session
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: SessionController::index
   📄 Fichier: SessionController.php

🔗 **admin_formation_index**
   📍 Chemin: /session/{year}/{page}/{programme}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::index2
   📄 Fichier: SessionController.php

🔗 **admin_formation_participants**
   📍 Chemin: /session/{id}/participants/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::participants
   📄 Fichier: SessionController.php

🔗 **admin_formation_preview_honorary**
   📍 Chemin: /formation/{formation}/preview-honorary/{person}/{type}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::previewHonorary
   📄 Fichier: FormationController.php

🔗 **admin_formation_show**
   📍 Chemin: /session/{id}/show/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::show
   📄 Fichier: SessionController.php

🔗 **admin_gotenberg_corps**
   📍 Chemin: gotenberg/gotenberg-corps
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: WebhookEdupratController::corps
   📄 Fichier: WebhookEdupratController.php

🔗 **admin_gotenberg_debug**
   📍 Chemin: gotenberg/gotenberg-debug
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: WebhookEdupratController::index
   📄 Fichier: WebhookEdupratController.php

🔗 **admin_gotenberg_debug_error**
   📍 Chemin: gotenberg/gotenberg-debug-error
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: WebhookEdupratController::error
   📄 Fichier: WebhookEdupratController.php

🔗 **admin_gotenberg_test**
   📍 Chemin: gotenberggotenberg
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: WebhookEdupratController::gotenbergTest
   📄 Fichier: WebhookEdupratController.php

🔗 **admin_indemnisation_create**
   📍 Chemin: /indemnisation-v2/create/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: IndemnisationController::create
   📄 Fichier: IndemnisationController.php

🔗 **admin_indemnisation_delete**
   📍 Chemin: /indemnisation-v2/{id}/delete/{year}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: IndemnisationController::delete
   📄 Fichier: IndemnisationController.php

🔗 **admin_indemnisation_edit**
   📍 Chemin: /indemnisation-v2/edit/{id}/{year}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: IndemnisationController::edit
   📄 Fichier: IndemnisationController.php

🔗 **admin_indemnisation_index**
   📍 Chemin: /indemnisation-v2/indemnisation-v2
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: IndemnisationController::index
   📄 Fichier: IndemnisationController.php

🔗 **admin_index**
   📍 Chemin: /
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::index
   📄 Fichier: DefaultController.php

🔗 **admin_lead_hisory_delete**
   📍 Chemin: /leads/{id}/deleteLeadHistory
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: LeadsController::delete
   📄 Fichier: LeadsController.php

🔗 **admin_lesson_create**
   📍 Chemin: /lesson/lesson
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: LessonController::create
   📄 Fichier: LessonController.php

🔗 **admin_lesson_delete**
   📍 Chemin: /lesson/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: LessonController::delete
   📄 Fichier: LessonController.php

🔗 **admin_lesson_duplicate**
   📍 Chemin: /elearning/{id}/duplicateLesson
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ElearningController::duplicateLesson
   📄 Fichier: ElearningController.php

🔗 **admin_lesson_edit**
   📍 Chemin: /lesson/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: LessonController::edit
   📄 Fichier: LessonController.php

🔗 **admin_lesson_updatePosition**
   📍 Chemin: /lesson/{id}/updatePosition
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: LessonController::updatePosition
   📄 Fichier: LessonController.php

🔗 **admin_participant_create**
   📍 Chemin: /participant/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::create
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_delete**
   📍 Chemin: /participant/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::delete
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_edit**
   📍 Chemin: /participant/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipantController::edit
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_export_emails**
   📍 Chemin: /participant/{id}/export-email-history
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipantController::exportMailerHistory
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_fusion**
   📍 Chemin: /participant/{participant1}/fusion/{participant2}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ParticipantController::fusion
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_index**
   📍 Chemin: /participant/participant
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::index
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_process_uga**
   📍 Chemin: /participant/process-uga
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::processUga
   📄 Fichier: ParticipantController.php

🔗 **admin_participant_show**
   📍 Chemin: /participant/{id}/show
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::show
   📄 Fichier: ParticipantController.php

🔗 **admin_participation_audit_show**
   📍 Chemin: /participation/participation
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipationController::auditShow
   📄 Fichier: ParticipationController.php

🔗 **admin_participation_delete**
   📍 Chemin: /participation/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: ParticipationController::delete
   📄 Fichier: ParticipationController.php

🔗 **admin_participation_edit**
   📍 Chemin: /participation/{id}/edit
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipationController::edit
   📄 Fichier: ParticipationController.php

🔗 **admin_participation_email**
   📍 Chemin: /participation/{id}/email/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipationController::sendEmail
   📄 Fichier: ParticipationController.php

🔗 **admin_participation_export_emails**
   📍 Chemin: /participation/{id}/export-email-history
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipationController::exportMailerHistory
   📄 Fichier: ParticipationController.php

🔗 **admin_participation_file**
   📍 Chemin: /participation_file/{id}/{field}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FilesController::participationFileDownload
   📄 Fichier: FilesController.php

🔗 **admin_participation_survey_show**
   📍 Chemin: /participation/{id}/survey/{surveyId}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ParticipationController::surveyShow
   📄 Fichier: ParticipationController.php

🔗 **admin_personal_data**
   📍 Chemin: /donnees-personnelles
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::personalData
   📄 Fichier: DefaultController.php

🔗 **admin_plaquette_clear_current_search**
   📍 Chemin: /plaquette/search/clear_current_searh
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::clearCurrentSearch
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_currentSelection**
   📍 Chemin: /plaquette/currentSelection/{selectionFormation}
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::currentSelection
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_dpf_delete**
   📍 Chemin: /plaquette/search/delete_dpf/{downloadPlaquetteFile}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::deleteDownloadPlaquetteFile
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_favori_delete**
   📍 Chemin: /plaquette/search/delete_favori/{selectionFormation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::deleteFavori
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_search**
   📍 Chemin: /plaquette/plaquette
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::search
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_search_add**
   📍 Chemin: /plaquette/search/add
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::addSearch
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_search_convert_as_favori**
   📍 Chemin: /plaquette/search/convert_as_favori
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::convertAsFavori
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_search_reinit**
   📍 Chemin: /plaquette/search/reinit
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::reinitSearch
   📄 Fichier: PlaquetteController.php

🔗 **admin_plaquette_search_save**
   📍 Chemin: /plaquette/search/save
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::saveSearch
   📄 Fichier: PlaquetteController.php

🔗 **admin_prise_en_charge_create**
   📍 Chemin: /prise-en-charge/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: PriseEnChargeController::create
   📄 Fichier: PriseEnChargeController.php

🔗 **admin_prise_en_charge_delete**
   📍 Chemin: /prise-en-charge/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: PriseEnChargeController::delete
   📄 Fichier: PriseEnChargeController.php

🔗 **admin_prise_en_charge_edit**
   📍 Chemin: /prise-en-charge/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: PriseEnChargeController::edit
   📄 Fichier: PriseEnChargeController.php

🔗 **admin_prise_en_charge_index**
   📍 Chemin: /prise-en-charge/prise-en-charge
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: PriseEnChargeController::index
   📄 Fichier: PriseEnChargeController.php

🔗 **admin_programme_autocomplete**
   📍 Chemin: /formation/autocomplete
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::autocompleteProgramme
   📄 Fichier: FormationController.php

🔗 **admin_programme_create**
   📍 Chemin: /formation/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::create
   📄 Fichier: FormationController.php

🔗 **admin_programme_create_select_type**
   📍 Chemin: /formation/create/select_type
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::selectCreate
   📄 Fichier: FormationController.php

🔗 **admin_programme_delete**
   📍 Chemin: /formation/delete/{id}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::delete
   📄 Fichier: FormationController.php

🔗 **admin_programme_dosearch**
   📍 Chemin: /formation/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FormationController::doSearch
   📄 Fichier: FormationController.php

🔗 **admin_programme_duplicate_check**
   📍 Chemin: /formation/duplicate-check
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::preventDuplicate
   📄 Fichier: FormationController.php

🔗 **admin_programme_edit**
   📍 Chemin: /formation/edit/{id}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::edit
   📄 Fichier: FormationController.php

🔗 **admin_programme_evaluation**
   📍 Chemin: /formation/{id}/evaluation
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::evaluation
   📄 Fichier: FormationController.php

🔗 **admin_programme_evaluation_former**
   📍 Chemin: /formation/{id}/evaluation-formateur
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::evaluationFormer
   📄 Fichier: FormationController.php

🔗 **admin_programme_export_emails**
   📍 Chemin: /formation/{id}/export-email-history
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::exportMailerHistory
   📄 Fichier: FormationController.php

🔗 **admin_programme_index**
   📍 Chemin: /formation/formation
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FormationController::index
   📄 Fichier: FormationController.php

🔗 **admin_programme_participant_export**
   📍 Chemin: /formation/exportParticipants/{programme}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: FormationController::exportParticipants
   📄 Fichier: FormationController.php

🔗 **admin_programme_session_list**
   📍 Chemin: /formation/sessionList/{programme}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FormationController::formationSessionList
   📄 Fichier: FormationController.php

🔗 **admin_send_to_api**
   📍 Chemin: /indemnisation-v2/{id}/sendToApi
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: IndemnisationController::sendToApi
   📄 Fichier: IndemnisationController.php

🔗 **admin_sib_webhook_desinscription**
   📍 Chemin: /sib/webhook/desinscription
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EdupratController::sibDesinscriptionWebHook
   📄 Fichier: EdupratController.php

🔗 **admin_suivi_cr**
   📍 Chemin: /suivi-cr/{person}/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::suiviCr
   📄 Fichier: DefaultController.php

🔗 **admin_suivi_cr_async**
   📍 Chemin: /suivi-cr/{person}/{year}/async/{field}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::suiviCrAsync
   📄 Fichier: DefaultController.php

🔗 **admin_suivi_cr_detail**
   📍 Chemin: /suivi-cr/{person}/{year}/{coordinator}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::suiviCrDetail
   📄 Fichier: DefaultController.php

🔗 **admin_survey_archive**
   📍 Chemin: /survey/{id}/archive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::archive
   📄 Fichier: SurveyController.php

🔗 **admin_survey_create**
   📍 Chemin: /survey/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::create
   📄 Fichier: SurveyController.php

🔗 **admin_survey_delete**
   📍 Chemin: /survey/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::delete
   📄 Fichier: SurveyController.php

🔗 **admin_survey_dofilter**
   📍 Chemin: /survey/filter/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::doFilter
   📄 Fichier: SurveyController.php

🔗 **admin_survey_duplicate**
   📍 Chemin: /survey/{id}/duplicate
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::duplicate
   📄 Fichier: SurveyController.php

🔗 **admin_survey_edit**
   📍 Chemin: /survey/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::edit
   📄 Fichier: SurveyController.php

🔗 **admin_survey_index**
   📍 Chemin: /survey/survey
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::index
   📄 Fichier: SurveyController.php

🔗 **admin_survey_question_category_create**
   📍 Chemin: /survey-question-category/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyQuestionCategoryController::create
   📄 Fichier: SurveyQuestionCategoryController.php

🔗 **admin_survey_question_category_delete**
   📍 Chemin: /survey-question-category/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyQuestionCategoryController::delete
   📄 Fichier: SurveyQuestionCategoryController.php

🔗 **admin_survey_question_category_edit**
   📍 Chemin: /survey-question-category/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyQuestionCategoryController::edit
   📄 Fichier: SurveyQuestionCategoryController.php

🔗 **admin_survey_question_category_index**
   📍 Chemin: /survey-question-category/survey-question-category
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyQuestionCategoryController::index
   📄 Fichier: SurveyQuestionCategoryController.php

🔗 **admin_survey_unarchive**
   📍 Chemin: /survey/{id}/unarchive
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: SurveyController::unarchive
   📄 Fichier: SurveyController.php

🔗 **admin_tag_create**
   📍 Chemin: /tag/create
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: TagController::create
   📄 Fichier: TagController.php

🔗 **admin_tag_delete**
   📍 Chemin: /tag/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: TagController::delete
   📄 Fichier: TagController.php

🔗 **admin_tag_edit**
   📍 Chemin: /tag/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: TagController::edit
   📄 Fichier: TagController.php

🔗 **admin_tag_index**
   📍 Chemin: /tag/tag
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: TagController::index
   📄 Fichier: TagController.php

🔗 **admin_tcs_groupequestiontcs_delete**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: GroupeQuestionTCSController::delete
   📄 Fichier: GroupeQuestionTCSController.php

🔗 **admin_tcs_groupequestiontcs_edit**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: GroupeQuestionTCSController::edit
   📄 Fichier: GroupeQuestionTCSController.php

🔗 **admin_topo_file**
   📍 Chemin: /topo_files/topo_files
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: TopoFilesController::topoFileDownload
   📄 Fichier: TopoFilesController.php

🔗 **admin_topo_programme_delete_file**
   📍 Chemin: /topo_files/{id}/fileProgrammeDelete/{formation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: TopoFilesController::topoProgrammeFileDelete
   📄 Fichier: TopoFilesController.php

🔗 **admin_topo_programme_file**
   📍 Chemin: /topo_files/{id}/fileProgramme/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: TopoFilesController::topoProgrammeFileDownload
   📄 Fichier: TopoFilesController.php

🔗 **admin_topo_tool_programme_file**
   📍 Chemin: /topo_files/{id}/fileToolProgramme/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: TopoFilesController::topoProgrammeToolFileDownload
   📄 Fichier: TopoFilesController.php

🔗 **admin_user_coordinators**
   📍 Chemin: /user/coordinators/{page}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_SUPERVISOR
   📁 Contrôleur: UserController::coordinators
   📄 Fichier: UserController.php

🔗 **admin_user_delete**
   📍 Chemin: /user/{id}/delete
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: UserController::delete
   📄 Fichier: UserController.php

🔗 **admin_user_dofilter**
   📍 Chemin: /user/filter/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_SUPERVISOR
   📁 Contrôleur: UserController::doFilter
   📄 Fichier: UserController.php

🔗 **admin_user_edit**
   📍 Chemin: /user/{id}/edit
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::edit
   📄 Fichier: UserController.php

🔗 **admin_user_evaluation**
   📍 Chemin: /user/{id}/suivi/evaluation/{formation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::evaluation
   📄 Fichier: UserController.php

🔗 **admin_user_evaluation_recap**
   📍 Chemin: /user/{id}/suivi/evaluation-recap/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::evaluationRecap
   📄 Fichier: UserController.php

🔗 **admin_user_file_cv**
   📍 Chemin: /user/{id}/file/cv
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::fileCv
   📄 Fichier: UserController.php

🔗 **admin_user_file_dli**
   📍 Chemin: /user/{id}/file/dli
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::fileDli
   📄 Fichier: UserController.php

🔗 **admin_user_index**
   📍 Chemin: /user/user
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_SUPERVISOR
   📁 Contrôleur: UserController::index
   📄 Fichier: UserController.php

🔗 **admin_user_monitoring**
   📍 Chemin: /user/{id}/suivi/{page}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::show
   📄 Fichier: UserController.php

🔗 **admin_user_register**
   📍 Chemin: /user/register
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: UserController::register
   📄 Fichier: UserController.php

🔗 **admin_user_reset**
   📍 Chemin: /user/reset
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::reset
   📄 Fichier: UserController.php

🔗 **admin_user_simulation**
   📍 Chemin: /user/simulation/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: UserController::simulation
   📄 Fichier: UserController.php

🔗 **admin_user_simulation_exit**
   📍 Chemin: /user/simulation_exit
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::simulationExit
   📄 Fichier: UserController.php

🔗 **admin_user_switch**
   📍 Chemin: /user/switch/{id}/{role}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::switch
   📄 Fichier: UserController.php

🔗 **admin_user_transfer**
   📍 Chemin: /user/{id}/transfer
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: UserController::transfer
   📄 Fichier: UserController.php

🔗 **alienor_crm_login**
   📍 Chemin: /login
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SecurityController::login
   📄 Fichier: SecurityController.php

🔗 **alienor_user_login**
   📍 Chemin: /login
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SecurityController::login
   📄 Fichier: SecurityController.php

🔗 **alienor_user_register_confirm**
   📍 Chemin: /user/register/confirm
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: UserController::confirm
   📄 Fichier: UserController.php

🔗 **allIsFactured**
   📍 Chemin: /session/{id}/allIsFactured
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::allIsFactured
   📄 Fichier: SessionController.php

🔗 **api_site_coordinateurs**
   📍 Chemin: /coordinateurs/coordinateurs
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: CoordinatorController::coordinateurs
   📄 Fichier: CoordinatorController.php

🔗 **api_site_formations**
   📍 Chemin: /formations/formations
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FormationController::formations
   📄 Fichier: FormationController.php

🔗 **api_site_places_restantes**
   📍 Chemin: places-restantes/{reference}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SessionController::placesRestantes
   📄 Fichier: SessionController.php

🔗 **api_site_professions**
   📍 Chemin: /professions/professions
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ProfessionController::profession
   📄 Fichier: ProfessionController.php

🔗 **app_admin_tcs_groupe_question_add_question**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionTCSController::new
   📄 Fichier: QuestionTCSController.php

🔗 **app_admin_tcs_participation_acces_tcs**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipationTcsAnswerController::accesTcs
   📄 Fichier: ParticipationTcsAnswerController.php

🔗 **app_admin_tcs_participation_answer**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipationTcsAnswerController::answerTcsQuestionGroup
   📄 Fichier: ParticipationTcsAnswerController.php

🔗 **app_admin_tcs_participation_answer_bilan**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipationTcsAnswerController::resultQuestionTCS
   📄 Fichier: ParticipationTcsAnswerController.php

🔗 **app_admin_tcs_participation_next_question**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipationTcsAnswerController::nextQuestion
   📄 Fichier: ParticipationTcsAnswerController.php

🔗 **app_admin_tcs_participation_synthese_educative**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipationTcsAnswerController::syntheseEducativeTCS
   📄 Fichier: ParticipationTcsAnswerController.php

🔗 **app_admin_tcs_question_tcs_delete**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionTCSController::delete
   📄 Fichier: QuestionTCSController.php

🔗 **app_admin_tcs_question_tcs_edit**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionTCSController::edit
   📄 Fichier: QuestionTCSController.php

🔗 **app_admin_tcs_question_tcs_expert_answers_delete**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::deleteExpertAnswers
   📄 Fichier: ExpertController.php

🔗 **app_admin_tcs_questionnaire_create_tcs_answer**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::createQuestionnaireTcsExpert
   📄 Fichier: ExpertController.php

🔗 **app_admin_tcs_questionnaire_tcs_add_groupe_question**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: GroupeQuestionTCSController::addGroupeQuestions
   📄 Fichier: GroupeQuestionTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_answer**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::indexAnswer
   📄 Fichier: ExpertController.php

🔗 **app_admin_tcs_questionnaire_tcs_answer_group**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::answerTcsQuestionGroup
   📄 Fichier: ExpertController.php

🔗 **app_admin_tcs_questionnaire_tcs_archive**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ArchiveQuestionnaireTCSController::archive
   📄 Fichier: ArchiveQuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_archive_index**
   📍 Chemin: 
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ArchiveQuestionnaireTCSController::index
   📄 Fichier: ArchiveQuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_delete**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::delete
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_duplicate**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::duplicate
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_edit**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::edit
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_index**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::index
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_new**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::new
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_show**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: QuestionnaireTCSController::show
   📄 Fichier: QuestionnaireTCSController.php

🔗 **app_admin_tcs_questionnaire_tcs_unarchive**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ArchiveQuestionnaireTCSController::unarchive
   📄 Fichier: ArchiveQuestionnaireTCSController.php

🔗 **app_eduprat_admin_bundle_controller_expert_delete**
   📍 Chemin: 
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::delete
   📄 Fichier: ExpertController.php

🔗 **app_eduprat_admin_bundle_controller_expert_edit**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::edit
   📄 Fichier: ExpertController.php

🔗 **app_eduprat_admin_bundle_controller_expert_index**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::index
   📄 Fichier: ExpertController.php

🔗 **app_eduprat_admin_bundle_controller_expert_new**
   📍 Chemin: 
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ExpertController::new
   📄 Fichier: ExpertController.php

🔗 **app_playwright_init**
   📍 Chemin: /playwright/playwright
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: PlaywrightController::init
   📄 Fichier: PlaywrightController.php

🔗 **bilan_generate_csv**
   📍 Chemin: /bilan-generate-csv/{year}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: DefaultController::bilanGenerateCsv
   📄 Fichier: DefaultController.php

🔗 **bilan_generate_csv_export**
   📍 Chemin: /export-csv/bilan-generate-csv-export/{type}/{start}/{end}/{jsonReferenceDate}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_SUPERVISOR
   📁 Contrôleur: ExportCSVController::bilanGenerateCsvExport
   📄 Fichier: ExportCSVController.php

🔗 **crm_analysis_commissions**
   📍 Chemin: /analysis/show/commissions/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::commissions
   📄 Fichier: AnalysisController.php

🔗 **crm_analysis_config**
   📍 Chemin: /analysis/analysis
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::config
   📄 Fichier: AnalysisController.php

🔗 **crm_analysis_coordinators**
   📍 Chemin: /analysis/show/coordinators
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::coordinators
   📄 Fichier: AnalysisController.php

🔗 **crm_analysis_data**
   📍 Chemin: /analysis/data/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::data
   📄 Fichier: AnalysisController.php

🔗 **crm_analysis_participants**
   📍 Chemin: /analysis/show/participants
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::participants
   📄 Fichier: AnalysisController.php

🔗 **crm_analysis_show**
   📍 Chemin: /analysis/show/{type}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AnalysisController::show
   📄 Fichier: AnalysisController.php

🔗 **crm_index**
   📍 Chemin: /
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::index
   📄 Fichier: DefaultController.php

🔗 **crm_leads_import**
   📍 Chemin: /leads/leads
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: LeadsController::import
   📄 Fichier: LeadsController.php

🔗 **crm_leads_suivi**
   📍 Chemin: /leads/suivi/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: LeadsController::indexLead
   📄 Fichier: LeadsController.php

🔗 **crm_participant_analyse**
   📍 Chemin: /participant/analyse/{page}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::analyse
   📄 Fichier: ParticipantController.php

🔗 **crm_reporting_actions**
   📍 Chemin: /reporting/reporting
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ReportingController::actions
   📄 Fichier: ReportingController.php

🔗 **crm_reporting_participants**
   📍 Chemin: /reporting/participants
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ReportingController::participants
   📄 Fichier: ReportingController.php

🔗 **crm_reporting_participants_repartition**
   📍 Chemin: /reporting/participants-repartition/{financeMode}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ReportingController::participantsRepartition
   📄 Fichier: ReportingController.php

🔗 **crm_updateLead**
   📍 Chemin: /leads/update/{id}/{leadContactDate}/{leadComment}/{leadCommentEduprat}/{leadState}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: LeadsController::updateLead
   📄 Fichier: LeadsController.php

🔗 **csv_export_file_get**
   📍 Chemin: /export-csv/export-csv
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_SUPERVISOR
   📁 Contrôleur: ExportCSVController::csvFileGet
   📄 Fichier: ExportCSVController.php

🔗 **csv_file_get**
   📍 Chemin: /csv-file-get/{year}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: DefaultController::csvFileGet
   📄 Fichier: DefaultController.php

🔗 **csv_lead_file_get**
   📍 Chemin: /leads/csv-lead-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: LeadsController::csvLeadFileGet
   📄 Fichier: LeadsController.php

🔗 **csv_missing_attestations_file_get**
   📍 Chemin: /comptabilite/csv-missing-attestations-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::csvMissingAttestationsFileGet
   📄 Fichier: ComptabiliteController.php

🔗 **csv_missing_modules_file_get**
   📍 Chemin: /comptabilite/csv-missing-modules-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::csvMissingModulesFileGet
   📄 Fichier: ComptabiliteController.php

🔗 **csv_participant_analysis_file_get**
   📍 Chemin: /participant/csv-participant-analysis-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::csvParticipantAnalysisFileGet
   📄 Fichier: ParticipantController.php

🔗 **csv_participant_file_get**
   📍 Chemin: /participant/csv-participant-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::csvParticipantFileGet
   📄 Fichier: ParticipantController.php

🔗 **csv_participations_history_file_get**
   📍 Chemin: /comptabilite/csv-participations-history-file-get
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::csvParticipationHistoryFileGet
   📄 Fichier: ComptabiliteController.php

🔗 **eduprat_activity_pdf_file**
   📍 Chemin: /{activity}/pdf-file
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ElearningCourseController::activityPdfFileDownload
   📄 Fichier: ElearningCourseController.php

🔗 **eduprat_audit_answer**
   📍 Chemin: /audit/{id}/{auditId}/{patient}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::answer
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_descriptif_audit_download**
   📍 Chemin: /descriptif-audit-download/{audit}/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::auditPdfFileDownloadOld
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_fiche_action**
   📍 Chemin: /fiche-action/{participation}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::auditFiche
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_formation_documents_pedagogiques**
   📍 Chemin: /documentsPedagogiquesProgramme/download/{id}/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::documentsPedagogiquesFileDownload
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_formation_topo**
   📍 Chemin: /topo/download/{id}/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::topoFileDownload
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_formation_topo_old**
   📍 Chemin: /{id}/file/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::topoFileDownloadOld
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_formation_topo_programme**
   📍 Chemin: /topoProgramme/download/{id}/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::topoProgrammeFileDownload
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_formation_topo_tool_programme**
   📍 Chemin: /toolProgramme/download/{id}/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::toolProgrammeFileDownload
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_index**
   📍 Chemin: /
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::index
   📄 Fichier: AuditController.php

🔗 **eduprat_audit_login**
   📍 Chemin: /login
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SecurityController::login
   📄 Fichier: SecurityController.php

🔗 **eduprat_audit_show**
   📍 Chemin: /audit/show/{id}/{auditId}/{patient}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::auditShow
   📄 Fichier: AuditController.php

🔗 **eduprat_evaluation**
   📍 Chemin: /evaluation-redirect/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EvaluationController::evaluationRedirect
   📄 Fichier: EvaluationController.php

🔗 **eduprat_evaluation_formation**
   📍 Chemin: /evaluation-formation/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EvaluationController::evaluationFormation
   📄 Fichier: EvaluationController.php

🔗 **eduprat_evaluation_former**
   📍 Chemin: /evaluation-former/{id}/{former}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EvaluationController::evaluationFormer
   📄 Fichier: EvaluationController.php

🔗 **eduprat_evaluation_global**
   📍 Chemin: /evaluation-global/{person}/{formation}/{page}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EvaluationController::evaluationGlobal
   📄 Fichier: EvaluationController.php

🔗 **eduprat_evaluation_redirect**
   📍 Chemin: /evaluation-redirect-front/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EvaluationController::evaluationRedirectFront
   📄 Fichier: EvaluationController.php

🔗 **eduprat_formation_elearning_download**
   📍 Chemin: /formation-elearning-download/{id}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::formationElearningDownload
   📄 Fichier: AuditController.php

🔗 **eduprat_front_clear_module**
   📍 Chemin: /participation-clear-module/{module}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::clearModule
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_contact**
   📍 Chemin: /contact
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::contact
   📄 Fichier: FrontController.php

🔗 **eduprat_front_elearning_validate**
   📍 Chemin: /elearning-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::elearningValidate
   📄 Fichier: FrontController.php

🔗 **eduprat_front_formation_files**
   📍 Chemin: /formation-files/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::formationFiles
   📄 Fichier: FrontController.php

🔗 **eduprat_front_formation_module**
   📍 Chemin: /formation-{module}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::formationPrerestitution
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_formation_prerestitution_validate**
   📍 Chemin: /formation-prerestitution-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::formationPrerestitutionValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_formation_restitution_validate**
   📍 Chemin: /formation-restitution-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::formationRestitutionValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_formations**
   📍 Chemin: /formations/{year}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::index
   📄 Fichier: FrontController.php

🔗 **eduprat_front_gdpr**
   📍 Chemin: /gdpr
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::gdpr
   📄 Fichier: FrontController.php

🔗 **eduprat_front_gdpr_notification**
   📍 Chemin: /gdpr-notification
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::gdprNotification
   📄 Fichier: FrontController.php

🔗 **eduprat_front_legals**
   📍 Chemin: /mentions-legales
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::legals
   📄 Fichier: FrontController.php

🔗 **eduprat_front_module_doc_pedagogique_1**
   📍 Chemin: /formation-docs-pedagogiques/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::docsPedagogiques
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_doc_pedagogique_1_file**
   📍 Chemin: /formation-docs-pedagogiques-{file}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::docsPedagogiquesFile
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_documents_pedagogiques_validate**
   📍 Chemin: /formation-validate-documents-pedagogique-{file}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::documentsPedagogiquesValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_elearning**
   📍 Chemin: /elearning/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ElearningCourseController::Elearning
   📄 Fichier: ElearningCourseController.php

🔗 **eduprat_front_module_elearning_activity**
   📍 Chemin: /activity-{activity}/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ElearningCourseController::activity
   📄 Fichier: ElearningCourseController.php

🔗 **eduprat_front_module_elearning_activity_quiz**
   📍 Chemin: /activity-{activity}/{question}/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ElearningCourseController::activityQuestion
   📄 Fichier: ElearningCourseController.php

🔗 **eduprat_front_module_elearning_lesson**
   📍 Chemin: /lesson-{lesson}/{participation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ElearningCourseController::lesson
   📄 Fichier: ElearningCourseController.php

🔗 **eduprat_front_module_end**
   📍 Chemin: /formation-finalisation/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::end
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_end_validate**
   📍 Chemin: /formation-finalisation-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::endValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_etutorat**
   📍 Chemin: /formation-etutorat-{stepId}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::etutorat2
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_etutorat_1**
   📍 Chemin: /formation-etutorat-1/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::etutorat1
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_etutorat_cas_clinique**
   📍 Chemin: /formation-etutorat-cas-clinique/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::etutoratCasClinique
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_fiche_action**
   📍 Chemin: /formation-fiche-action-{stepId}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::fiche
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_fiche_action_download**
   📍 Chemin: /formation-fiche-action-download/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::ficheActionDownload
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_progression**
   📍 Chemin: /formation-fiche-progression/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::progression
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_synthese**
   📍 Chemin: /formation-fiche-synthese/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::synthese
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_tool_box**
   📍 Chemin: /formation-tool-box/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::toolBox
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_tool_box_file**
   📍 Chemin: /formation-tool-box-{file}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::toolBoxFile
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_tool_box_validate**
   📍 Chemin: /formation-validate-tool-box-{file}/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::toolBoxValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_topos**
   📍 Chemin: /formation-topos/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::topos
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_topos_validate**
   📍 Chemin: /formation-topos-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::toposValidate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_video_1**
   📍 Chemin: /formation-video-1/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::video1
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_video_1_validate**
   📍 Chemin: /formation-video-1-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::video1Validate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_video_2**
   📍 Chemin: /formation-video-2/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::video2
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_module_video_2_validate**
   📍 Chemin: /formation-video-2-validate/{id}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::video2Validate
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_next_module**
   📍 Chemin: /participation-next-module/{participation}/{module}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::nextModule
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_notification**
   📍 Chemin: /notification
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::notification
   📄 Fichier: FrontController.php

🔗 **eduprat_front_personal_data**
   📍 Chemin: /donnees-personnelles
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::personalData
   📄 Fichier: FrontController.php

🔗 **eduprat_front_profile**
   📍 Chemin: /profile
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::profile
   📄 Fichier: FrontController.php

🔗 **eduprat_front_profile_edit**
   📍 Chemin: /profile/edit
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::edit
   📄 Fichier: FrontController.php

🔗 **eduprat_front_refresh_module_time**
   📍 Chemin: /participation-refresh-module-time/{id}/{moduleId}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::refreshModuleTime
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_refresh_time**
   📍 Chemin: /participation-refresh-time/{id}/{stepId}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ModuleController::refreshTime
   📄 Fichier: ModuleController.php

🔗 **eduprat_front_tutorial**
   📍 Chemin: /tutorial
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::tutorial
   📄 Fichier: FrontController.php

🔗 **eduprat_front_tutorial_completed**
   📍 Chemin: /tutorial-completed
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::tutorialCompleted
   📄 Fichier: FrontController.php

🔗 **eduprat_password_edit**
   📍 Chemin: /user/password-edit
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::editPassword
   📄 Fichier: UserController.php

🔗 **eduprat_password_reset**
   📍 Chemin: /user/password-reset/{token}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: UserController::passwordReset
   📄 Fichier: UserController.php

🔗 **eduprat_survey_answer**
   📍 Chemin: /survey/{id}/{surveyId}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SurveyController::answer
   📄 Fichier: SurveyController.php

🔗 **eduprat_survey_show**
   📍 Chemin: /survey/show/{id}/{surveyId}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SurveyController::surveyShow
   📄 Fichier: SurveyController.php

🔗 **evaluation_coordinator_by_coordinator**
   📍 Chemin: /evaluation/evaluation
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationCoordinatorbyCoordinator
   📄 Fichier: EvaluationController.php

🔗 **evaluation_coordinator_by_coordinator_show**
   📍 Chemin: /formation/{programme}/evaluation-coordinator/coordinator/{coordinator}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::evaluationCoordinatorByCoordinatorShow
   📄 Fichier: FormationController.php

🔗 **evaluation_formation_global_former**
   📍 Chemin: /evaluation/formation/{formation}/former/{former}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationGlobalFormer
   📄 Fichier: EvaluationController.php

🔗 **evaluation_formation_global_role**
   📍 Chemin: /evaluation/formation/{formation}/{role}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: EvaluationController::evaluationGlobalFormation
   📄 Fichier: EvaluationController.php

🔗 **evaluation_former_by_coordinator**
   📍 Chemin: /evaluation/programme/{programme}/former/{former}/coordinator/{coordinator}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationFormerByCoordinator
   📄 Fichier: EvaluationController.php

🔗 **evaluation_former_by_coordinator_show**
   📍 Chemin: /formation/{programme}/evaluation/former/{former}/coordinator/{coordinator}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::evaluationFormerByCoordinatorShow
   📄 Fichier: FormationController.php

🔗 **evaluation_global_coordinator_year**
   📍 Chemin: /evaluation/coordinator/{person}/{year}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationGlobalCoordinatorYear
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_former_year**
   📍 Chemin: /evaluation/former/{person}/{year}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationGlobalFormerYear
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_topo**
   📍 Chemin: /evaluation/topo/{year}/{role}/{title}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationGlobalTopo
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_topo_index**
   📍 Chemin: /evaluation/topo/{year}/{page}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationGlobalTopoList
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_topo_list_dosearch**
   📍 Chemin: /evaluation/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::doSearch
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_user_index**
   📍 Chemin: /evaluation/user/{page}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: EvaluationController::evaluationGlobalUserList
   📄 Fichier: EvaluationController.php

🔗 **evaluation_global_user_list_dosearch**
   📍 Chemin: /evaluation/user/
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_WEBMASTER
   📁 Contrôleur: EvaluationController::doSearchUser
   📄 Fichier: EvaluationController.php

🔗 **evaluation_programme_by_coordinator**
   📍 Chemin: /evaluation/programme/{programme}/coordinator/{coordinator}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: EvaluationController::evaluationProgrammebyCoordinator
   📄 Fichier: EvaluationController.php

🔗 **evaluation_programme_by_coordinator_show**
   📍 Chemin: /formation/{programme}/evaluation-programme/coordinator/{coordinator}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: FormationController::evaluationProgrammeByCoordinatorShow
   📄 Fichier: FormationController.php

🔗 **evaluation_programme_global_role**
   📍 Chemin: /evaluation/programme/{programme}/{role}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: EvaluationController::evaluationGlobalProgramme
   📄 Fichier: EvaluationController.php

🔗 **export-csv-type**
   📍 Chemin: /export-csv/
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: ExportCSVController::coordinators
   📄 Fichier: ExportCSVController.php

🔗 **factureFinanceSousMode**
   📍 Chemin: /session/{formation}/factureFinanceSousMode/{financeSousMode}/{n1}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::factureFinanceSousMode
   📄 Fichier: SessionController.php

🔗 **front_attestation_delete_file**
   📍 Chemin: /{id}/attestation-deletefile/{n1}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::attestationDeleteFileDownload
   📄 Fichier: FrontController.php

🔗 **front_attestation_file**
   📍 Chemin: /{id}/attestation-file/{n1}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::attestationFileDownload
   📄 Fichier: FrontController.php

🔗 **imap**
   📍 Chemin: /imap
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SecurityController::imap
   📄 Fichier: SecurityController.php

🔗 **lead_generate_csv_export**
   📍 Chemin: /leads/lead-generate-csv-export
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: LeadsController::leadGenerateCsvExport
   📄 Fichier: LeadsController.php

🔗 **paidCoordinator**
   📍 Chemin: /session/{id}/{midCourse}/paidCoordinateur
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::paidCoordinator
   📄 Fichier: SessionController.php

🔗 **paidFormateur**
   📍 Chemin: /session/{id}/paidFormateur
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::paidFormateur
   📄 Fichier: SessionController.php

🔗 **participant_action_sheet**
   📍 Chemin: /{id}/participantFile/{fileField}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::participantActionSheetDownload
   📄 Fichier: FrontController.php

🔗 **participant_date_download**
   📍 Chemin: /participant-date-download/participant-date-download
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: ParticipantDateDownloadController::edit
   📄 Fichier: ParticipantDateDownloadController.php

🔗 **participant_end_action**
   📍 Chemin: /participant_end_action
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::participantEndCurrent
   📄 Fichier: FrontController.php

🔗 **participant_end_action_logout**
   📍 Chemin: /participant-logout
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FrontController::participantLogout
   📄 Fichier: FrontController.php

🔗 **participant_generate_csv_export**
   📍 Chemin: /participant/participant-generate-csv-export
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::participantGenerateCsvExport
   📄 Fichier: ParticipantController.php

🔗 **participant_generate_csv_export_analysis**
   📍 Chemin: /participant/participant-generate-csv-export-analysis
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::participantGenerateCsvExportAnalysis
   📄 Fichier: ParticipantController.php

🔗 **participant_generate_csv_export_sendinblue**
   📍 Chemin: /participant/participant-generate-csv-export-sib
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::participantGenerateCsvExportSendinblue
   📄 Fichier: ParticipantController.php

🔗 **participant_history_generate_csv_export**
   📍 Chemin: /comptabilite/participant-history-generate-csv-export
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::participantHistoryGenerateCsvExport
   📄 Fichier: ComptabiliteController.php

🔗 **participant_updateStatus**
   📍 Chemin: /participant/update/{id}/{status}
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ParticipantController::updateLead
   📄 Fichier: ParticipantController.php

🔗 **participation_generate_missing_attestations**
   📍 Chemin: /comptabilite/participation-generate-missing-attestations-export
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::participantGenerateMissingAttestationsCsvExport
   📄 Fichier: ComptabiliteController.php

🔗 **participation_generate_missing_modules**
   📍 Chemin: /comptabilite/participation-generate-missing-modules-export
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: ComptabiliteController::participantGenerateMissingModulesCsvExport
   📄 Fichier: ComptabiliteController.php

🔗 **pdf_action_empty_pdf**
   📍 Chemin: /pdf/fiche-action/empty/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: FicheActionEmptyController::ficheActionEmpty
   📄 Fichier: FicheActionEmptyController.php

🔗 **pdf_attestation-presence_html**
   📍 Chemin: /pdf/attestation-presence/{id}/{financeSousMode}/html/{start}/{batch}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::attestationPresenceDocument
   📄 Fichier: HtmlController.php

🔗 **pdf_attestation-presence_pdf**
   📍 Chemin: /pdf/attestation-presence/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::attestationPresence
   📄 Fichier: DefaultController.php

🔗 **pdf_attestation_honneur**
   📍 Chemin: /pdf/attestation-honneur/{id}/{participant}/{person}/{n1}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::attestationHonneur
   📄 Fichier: HtmlController.php

🔗 **pdf_attestation_honneur_pdf**
   📍 Chemin: /pdf/attestation-honneur/{id}/{participant}/{person}/{n1}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::attestationHonneur
   📄 Fichier: DefaultController.php

🔗 **pdf_audit_answers_html**
   📍 Chemin: /pdf/audit-answers/{id}/{financeSousMode}/{auditId}/{start}/{batch}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::auditAnswers
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_answers_pdf**
   📍 Chemin: /pdf/audit-answers/{id}/{financeSousMode}/{auditId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::auditAnswers
   📄 Fichier: DefaultController.php

🔗 **pdf_audit_empty_html**
   📍 Chemin: /pdf/audit/empty/{id}/{auditId}/{patient}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::auditEmpty
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_empty_pdf**
   📍 Chemin: /pdf/audit/empty/{id}/{auditId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::auditEmpty
   📄 Fichier: DefaultController.php

🔗 **pdf_audit_pdf**
   📍 Chemin: /pdf/audit/{id}/{auditId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::audit
   📄 Fichier: AuditController.php

🔗 **pdf_audit_pdf_patient**
   📍 Chemin: /pdf/audit/{id}/{auditId}/{patient}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: AuditController::auditPatient
   📄 Fichier: AuditController.php

🔗 **pdf_audit_restitution**
   📍 Chemin: /pdf/restitution-audit/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::restitutionAudit
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_restitution_groupe**
   📍 Chemin: /pdf/restitution-audit-groupe/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::restitutionAuditGroupe
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_restitution_groupe_2**
   📍 Chemin: /pdf/restitution-audit-groupe-2/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::restitutionAuditGroupe2
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_restitution_groupe_individuelle**
   📍 Chemin: /pdf/restitution-audit-groupe-individuelle/{participation}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::restitutionAuditGroupeIndividuelle
   📄 Fichier: HtmlController.php

🔗 **pdf_audit_restitution_groupe_individuelle_pdf**
   📍 Chemin: /pdf/restitution-audit-groupe-individuelle/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::restitutionAuditGroupeIndividuelle
   📄 Fichier: DefaultController.php

🔗 **pdf_auto_evaluations**
   📍 Chemin: /pdf/auto-evaluations/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::autoEvaluations
   📄 Fichier: HtmlController.php

🔗 **pdf_auto_evaluations_pdf**
   📍 Chemin: /pdf/auto-evaluations/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::autoEvals
   📄 Fichier: DefaultController.php

🔗 **pdf_budget_cr_total_pdf**
   📍 Chemin: /pdf/admin_budget_cr_total/{formation}/{coordinator}/{n1}/{token}/{tokenFormation}/pdf
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: BudgetCrTotalController::budgetCrTotal
   📄 Fichier: BudgetCrTotalController.php

🔗 **pdf_certificate_attendance_html**
   📍 Chemin: /pdf/certificate-attendance/{id}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::certificateAttendance
   📄 Fichier: HtmlController.php

🔗 **pdf_certificate_attendance_pdf**
   📍 Chemin: /pdf/pdf
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::certificateAttendance
   📄 Fichier: DefaultController.php

🔗 **pdf_certificate_participation_horary_formation_html**
   📍 Chemin: /pdf/certificate-participation-horary-formation/{id}/{financeSousMode}/html/{start}/{batch}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::certificateParticipationHoraryFormation
   📄 Fichier: HtmlController.php

🔗 **pdf_certificate_participation_horary_formation_opti_html**
   📍 Chemin: /pdf/certificate-participation-horary-formation-opti/{id}/{financeSousMode}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::certificateParticipationHoraryOptiFormation
   📄 Fichier: HtmlController.php

🔗 **pdf_certificate_participation_horary_html**
   📍 Chemin: /pdf/certificate-participation-horary/{id}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::certificateParticipationHonorary
   📄 Fichier: HtmlController.php

🔗 **pdf_certificate_participation_horary_pdf**
   📍 Chemin: /pdf/certificate-participation-horary/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::certificateParticipationHorary
   📄 Fichier: DefaultController.php

🔗 **pdf_certificate_participation_pdf**
   📍 Chemin: /pdf/certificate-participation/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::certificateParticipation
   📄 Fichier: DefaultController.php

🔗 **pdf_contract_former_html**
   📍 Chemin: /pdf/contract-former/{id}/{formation}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::formerContract
   📄 Fichier: HtmlController.php

🔗 **pdf_contract_former_pdf**
   📍 Chemin: /pdf/contract-former/{id}/{formation}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::contractFormer
   📄 Fichier: DefaultController.php

🔗 **pdf_convention_html**
   📍 Chemin: /pdf/convention/{id}/{financeSousMode}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::convention
   📄 Fichier: HtmlController.php

🔗 **pdf_convention_pdf**
   📍 Chemin: /pdf/convention/{id}/{financeSousMode}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::convention
   📄 Fichier: DefaultController.php

🔗 **pdf_convention_pharmacie_html**
   📍 Chemin: /pdf/convention-pharmacie/{id}/{financeSousMode}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::conventionPharmacie
   📄 Fichier: HtmlController.php

🔗 **pdf_convention_pharmacie_pdf**
   📍 Chemin: /pdf/convention-pharmacie/{id}/{financeSousMode}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::conventionPharmacie
   📄 Fichier: DefaultController.php

🔗 **pdf_coordinator_honorary_html**
   📍 Chemin: /pdf/coordinator_honorary/{formation}/{coordinator}/{n1}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::coordinatorHonorary
   📄 Fichier: HtmlController.php

🔗 **pdf_coordinator_honorary_pdf**
   📍 Chemin: /pdf/coordinator_honorary/{formation}/{coordinator}/{n1}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::coordinatorHonorary
   📄 Fichier: DefaultController.php

🔗 **pdf_coordinator_table_pdf**
   📍 Chemin: /pdf/coordinator-table/{id}/pdf
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::coordinatorTable
   📄 Fichier: DefaultController.php

🔗 **pdf_document_realisation_formation_html**
   📍 Chemin: /pdf/document_realisation/{id}/{financeSousMode}/html/{start}/{batch}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::documentRealisationFormation
   📄 Fichier: HtmlController.php

🔗 **pdf_emargement_html**
   📍 Chemin: /pdf/emargement/{id}/{financeSousMode}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::emargement
   📄 Fichier: HtmlController.php

🔗 **pdf_emargement_pdf**
   📍 Chemin: pdf/emargement/{id}/{financeSousMode}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: EmargementPdfController::emargement
   📄 Fichier: EmargementPdfController.php

🔗 **pdf_etutorat_empty_html**
   📍 Chemin: /pdf/etutorat/empty/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::etutoratEmpty
   📄 Fichier: HtmlController.php

🔗 **pdf_etutorat_empty_pdf**
   📍 Chemin: /pdf/etutorat/empty/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::etutoratEmpty
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_coordinator_by_coordinator_html**
   📍 Chemin: /pdf/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/html/
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::evaluationCoordinatorByCoordinator
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_coordinator_by_coordinator_pdf**
   📍 Chemin: /pdf/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::evaluationCoordinatorByCoordinator
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_formation_global_former**
   📍 Chemin: /pdf/formation/{formation}/former/{former}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalFormer
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_formation_global_former_pdf**
   📍 Chemin: /pdf/programme/{formation}/former/{former}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalFormer
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_formation_global_role**
   📍 Chemin: /pdf/formation/{formation}/{role}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalFormation
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_formation_global_role_pdf**
   📍 Chemin: /pdf/formation/{formation}/{role}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalFormation
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_global_coordinator_year**
   📍 Chemin: /pdf/coordinator/{person}/{year}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalCoordinatorYear
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_global_coordinator_year_pdf**
   📍 Chemin: /pdf/coordinator/{person}/{year}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalCoordinatorYear
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_global_former_year**
   📍 Chemin: /pdf/former/{person}/{year}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalFormerYear
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_global_former_year_pdf**
   📍 Chemin: /pdf/former/{person}/{year}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalFormerYear
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_global_topo**
   📍 Chemin: /pdf/topo/{year}/{role}/{title}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalTopo
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_global_topo_pdf**
   📍 Chemin: /pdf/topo/{year}/{role}/{title}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalTopo
   📄 Fichier: DefaultController.php

🔗 **pdf_evaluation_programme_global_role**
   📍 Chemin: /pdf/programme/{programme}/{role}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::pdfEvaluationGlobalProgramme
   📄 Fichier: HtmlController.php

🔗 **pdf_evaluation_programme_global_role_pdf**
   📍 Chemin: /pdf/programme/{programme}/{role}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::pdfEvaluationGlobalProgramme
   📄 Fichier: DefaultController.php

🔗 **pdf_flyer_html**
   📍 Chemin: /pdf/flyer/html/{id1}/{id2}/{id3}/{id4}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::flyer
   📄 Fichier: HtmlController.php

🔗 **pdf_flyer_pdf**
   📍 Chemin: /pdf/flyer/pdf/{user}/{tokenUser}/{id}/{token}
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::flyer
   📄 Fichier: DefaultController.php

🔗 **pdf_footer_audit**
   📍 Chemin: /pdf/footer-audit
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerAudit
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_budget_coordinator**
   📍 Chemin: /pdf/footer-budget-coordinator
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerBudgetCoordinator
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_contract**
   📍 Chemin: /pdf/footer-contract
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerContract
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_coordinateur**
   📍 Chemin: /pdf/footer-coordinateur/{coordinateur}/{hasDPC}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerCoordinateur
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_dpc**
   📍 Chemin: /pdf/footer-dpc
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerDpc
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_evaluation**
   📍 Chemin: /pdf/footer-evaluation
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerEvaluation
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_evaluation_global**
   📍 Chemin: /pdf/footer-evaluation-global
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerEvaluationGlobal
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_plaquette_programme**
   📍 Chemin: /pdf/footer-plaquette-programme
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerPlaquette
   📄 Fichier: HtmlController.php

🔗 **pdf_footer_restitution**
   📍 Chemin: /pdf/footer-restitution
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::footerRestitution
   📄 Fichier: HtmlController.php

🔗 **pdf_formation_generate_file**
   📍 Chemin: /pdf/formation-generate-file/{type}/{id}/{financeSousMode}/generate/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::formationGenerateFile
   📄 Fichier: DefaultController.php

🔗 **pdf_formation_generate_file_get**
   📍 Chemin: /pdf/formation-generate-file-get/{type}/{id}/generate/{token}/{financeSousMode}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::formationGenerateFileGet
   📄 Fichier: DefaultController.php

🔗 **pdf_fusion_header_html**
   📍 Chemin: /pdf/fusion-header/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::fusionHeader
   📄 Fichier: HtmlController.php

🔗 **pdf_fusion_header_pdf**
   📍 Chemin: /pdf/fusion-header/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::fusionHeader
   📄 Fichier: DefaultController.php

🔗 **pdf_fusion_methodologie_html**
   📍 Chemin: /pdf/fusion-methodologie/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::fusionMethodologie
   📄 Fichier: HtmlController.php

🔗 **pdf_fusion_methodologie_pdf**
   📍 Chemin: /pdf/fusion-methodologie/{id}/pdf
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::fusionMethodologie
   📄 Fichier: DefaultController.php

🔗 **pdf_fusion_summary_html**
   📍 Chemin: /pdf/fusion-summary/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::fusionSummary
   📄 Fichier: HtmlController.php

🔗 **pdf_fusion_summary_pdf**
   📍 Chemin: /pdf/fusion-summary/pdf
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::fusionSummary
   📄 Fichier: DefaultController.php

🔗 **pdf_header_admin_budget_cr_total**
   📍 Chemin: /pdf/header-admin_budget_cr_total
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerAdminBudgetCrTotal
   📄 Fichier: HtmlController.php

🔗 **pdf_header_attestation_presence_document**
   📍 Chemin: /pdf/header-attestation-presence-document
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerAttestationPresenceDocument
   📄 Fichier: HtmlController.php

🔗 **pdf_header_coordinator_honorary**
   📍 Chemin: /pdf/header-coordinator_honorary
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerCoordinatorHonorary
   📄 Fichier: HtmlController.php

🔗 **pdf_header_dpc_attendance**
   📍 Chemin: /pdf/header-dpc-attendance
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerDpcAttendance
   📄 Fichier: HtmlController.php

🔗 **pdf_header_evaluation_global**
   📍 Chemin: /pdf/header-evaluation-global/{id}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerEvaluationGlobal
   📄 Fichier: HtmlController.php

🔗 **pdf_header_participation**
   📍 Chemin: /pdf/pdf
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerParticipation
   📄 Fichier: HtmlController.php

🔗 **pdf_header_realisation**
   📍 Chemin: /pdf/header-realisation/{id}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerRealisation
   📄 Fichier: HtmlController.php

🔗 **pdf_header_restitution**
   📍 Chemin: /pdf/header-restitution
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::headerRestitution
   📄 Fichier: HtmlController.php

🔗 **pdf_invitation_html**
   📍 Chemin: /pdf/invitation/{user}/{id}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::invitation
   📄 Fichier: HtmlController.php

🔗 **pdf_invitation_pdf**
   📍 Chemin: /pdf/invitation/{user}/{id}/pdf/{tokenUser}/{token}
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::invitation
   📄 Fichier: DefaultController.php

🔗 **pdf_invoice_pdf**
   📍 Chemin: pdf/invoice/{id}/{financeSousMode}/{n1}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: InvoicePdfController::invoice
   📄 Fichier: InvoicePdfController.php

🔗 **pdf_invoice_proforma_pdf**
   📍 Chemin: /pdf/invoice_proforma/{id}/{financeSousMode}/{n1}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: InvoicePdfController::invoiceProforma
   📄 Fichier: InvoicePdfController.php

🔗 **pdf_programmes_html**
   📍 Chemin: /pdf/programmes/{id}/{favori}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::programme
   📄 Fichier: HtmlController.php

🔗 **pdf_programmes_pdf**
   📍 Chemin: /pdf/programmes/{id}/{favori}/pdf
   🌐 Méthodes HTTP: POST
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::programmes
   📄 Fichier: DefaultController.php

🔗 **pdf_progression_empty_html**
   📍 Chemin: /pdf/fiche-progression/empty/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::progressionEmpty
   📄 Fichier: HtmlController.php

🔗 **pdf_progression_empty_pdf**
   📍 Chemin: /pdf/fiche-progression/empty/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::progressionEmpty
   📄 Fichier: DefaultController.php

🔗 **pdf_restitution_actalians_pdf**
   📍 Chemin: /pdf/restitution-actalians/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::restitutionActalians
   📄 Fichier: DefaultController.php

🔗 **pdf_restitution_audit_groupe_2_pdf**
   📍 Chemin: /pdf/restitution-audit-groupe-2/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::restitutionAuditGroupe2
   📄 Fichier: DefaultController.php

🔗 **pdf_restitution_audit_groupe_pdf**
   📍 Chemin: /pdf/restitution-audit-groupe/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::restitutionAuditGroupe
   📄 Fichier: DefaultController.php

🔗 **pdf_restitution_audit_pdf**
   📍 Chemin: /pdf/restitution-audit/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::restitutionFormation
   📄 Fichier: DefaultController.php

🔗 **pdf_survey_answers_html**
   📍 Chemin: /pdf/survey-answers/{id}/{financeSousMode}/{surveyId}/{start}/{batch}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::surveyAnswers
   📄 Fichier: HtmlController.php

🔗 **pdf_survey_answers_pdf**
   📍 Chemin: /pdf/survey-answers/{id}/{financeSousMode}/{surveyId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::surveyAnswers
   📄 Fichier: DefaultController.php

🔗 **pdf_survey_empty_pdf**
   📍 Chemin: /survey/empty/{id}/{surveyId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: SurveyEmptyController::surveyEmpty
   📄 Fichier: SurveyEmptyController.php

🔗 **pdf_survey_html**
   📍 Chemin: /pdf/survey/{id}/{surveyId}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::survey
   📄 Fichier: HtmlController.php

🔗 **pdf_survey_pdf**
   📍 Chemin: /pdf/survey/{id}/{surveyId}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::survey
   📄 Fichier: DefaultController.php

🔗 **pdf_synthese_empty_html**
   📍 Chemin: /pdf/fiche-synthese/empty/{id}/{participation}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::syntheseEmpty
   📄 Fichier: HtmlController.php

🔗 **pdf_synthese_empty_pdf**
   📍 Chemin: /pdf/fiche-synthese/empty/{id}/{participation}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::syntheseEmpty
   📄 Fichier: DefaultController.php

🔗 **pdf_synthese_full_html**
   📍 Chemin: /pdf/fiche-synthese/full/{id}/{participation}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::syntheseFull
   📄 Fichier: HtmlController.php

🔗 **pdf_synthese_full_pdf**
   📍 Chemin: /pdf/fiche-synthese/full/{id}/{participation}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::syntheseFull
   📄 Fichier: DefaultController.php

🔗 **pdf_tcs_answers_html**
   📍 Chemin: /pdf/tcs-answers/{id}/{financeSousMode}/html
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::tcsAnswers
   📄 Fichier: HtmlController.php

🔗 **pdf_tcs_empty_html**
   📍 Chemin: /pdf/tcs/empty/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::tcsEmpty
   📄 Fichier: HtmlController.php

🔗 **pdf_tcs_empty_pdf**
   📍 Chemin: /pdf/tcs/empty/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::tcsEmpty
   📄 Fichier: DefaultController.php

🔗 **pdf_tcs_html**
   📍 Chemin: /pdf/tcs/{id}/html
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: HtmlController::tcs
   📄 Fichier: HtmlController.php

🔗 **pdf_tcs_pdf**
   📍 Chemin: /pdf/tcs/{id}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: DefaultController::tcs
   📄 Fichier: DefaultController.php

🔗 **pdf_traceability_document_pdf**
   📍 Chemin: pdf/traceability-document/{id}/{financeSousMode}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: traceabilityPdfController::traceabilityDocument
   📄 Fichier: traceabilityPdfController.php

🔗 **pdf_traceability_document_unity_pdf**
   📍 Chemin: pdf/traceability-document-unity/{id}/{financeSousMode}/{unity}/pdf/{token}
   🌐 Méthodes HTTP: GET
   🔐 Rôle requis: AUCUN RÔLE SPÉCIFIQUE
   📁 Contrôleur: traceabilityPdfController::traceabilityDocumentUnity
   📄 Fichier: traceabilityPdfController.php

🔗 **plaquette_delete_favori_temporaire**
   📍 Chemin: /plaquette/search/delete_favori_temporaire/{parentSelectionFormation}
   🌐 Méthodes HTTP: ANY
   🔐 Rôle requis: ROLE_COORDINATOR
   📁 Contrôleur: PlaquetteController::deleteTemporaryFavori
   📄 Fichier: PlaquetteController.php

🔗 **refresh_facture_state**
   📍 Chemin: /session/{id}/refreshFactureState
   🌐 Méthodes HTTP: GET,POST
   🔐 Rôle requis: ROLE_COORDINATOR_LBI
   📁 Contrôleur: SessionController::refreshFactureState
   📄 Fichier: SessionController.php


================================================================================
HIÉRARCHIE DES RÔLES (depuis security.yaml):
================================================================================
ROLE_USER (base)
├── ROLE_FORMER
├── ROLE_COORDINATOR_LBI
│   └── ROLE_COORDINATOR
│       ├── ROLE_SUPERVISOR
│       │   └── ROLE_SUPERVISOR_FRANCE
│       └── ROLE_FORMER_PHARMACIE
└── ROLE_WEBMASTER (hérite de FORMER, COORDINATOR, SUPERVISOR, SUPERVISOR_FRANCE)
    └── ROLE_WEBMASTER_COMPTA
        └── ROLE_SUPER_ADMIN (+ ROLE_ALLOWED_TO_SWITCH)

================================================================================
ZONES D'ACCÈS PRINCIPALES:
================================================================================
• /gestion/* - Zone Admin (ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE)
• /crm/* - Zone CRM (ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE)
• /pdf/* - Zone PDF (PUBLIC_ACCESS)
• /api/* - Zone API (PUBLIC_ACCESS pour certaines, IS_AUTHENTICATED_FULLY pour d'autres)
• / - Zone Audit/Front (IS_AUTHENTICATED_FULLY)
