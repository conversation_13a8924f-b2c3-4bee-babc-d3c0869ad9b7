<?php

use Rector\Config\RectorConfig;
use <PERSON>\Doctrine\Set\DoctrineSetList;
use <PERSON>\Php54\Rector\Array_\LongArrayToShortArrayRector;
use <PERSON>\Php74\Rector\Closure\ClosureToArrowFunctionRector;
use <PERSON>\Php80\Rector\ClassConstFetch\ClassOnThisVariableObjectRector;
use <PERSON>\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use Rector\Set\ValueObject\LevelSetList;
use Rector\Symfony\Set\SensiolabsSetList;
use Rector\Symfony\Set\SymfonySetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->symfonyContainerXml(__DIR__ . '/var/cache/dev/App_KernelDevDebugContainer.xml');
    $rectorConfig->symfonyContainerPhp(__DIR__ . '/tests/symfony-container.php');

    $rectorConfig->importNames();
    $rectorConfig->importShortClasses(false);
    $rectorConfig->removeUnusedImports();

    $rectorConfig->paths([
        __DIR__ . '/config',
        __DIR__ . '/src',
        __DIR__ . '/tests',
//        __DIR__ . '/vendor/alienor/',
    ]);

    $rectorConfig->sets([
//        LevelSetList::UP_TO_PHP_83,
        SymfonySetList::SYMFONY_71,
        SymfonySetList::SYMFONY_70,
        SymfonySetList::SYMFONY_64,
        SymfonySetList::SYMFONY_63,
        SymfonySetList::SYMFONY_62,
        SymfonySetList::SYMFONY_61,
        SymfonySetList::SYMFONY_54,
        SymfonySetList::SYMFONY_CODE_QUALITY,
        SymfonySetList::SYMFONY_CONSTRUCTOR_INJECTION,
        DoctrineSetList::ANNOTATIONS_TO_ATTRIBUTES,
        SymfonySetList::ANNOTATIONS_TO_ATTRIBUTES,
        SensiolabsSetList::ANNOTATIONS_TO_ATTRIBUTES,
        DoctrineSetList::TYPED_COLLECTIONS,
        DoctrineSetList::DOCTRINE_CODE_QUALITY,
        DoctrineSetList::DOCTRINE_DBAL_30,
    ]);

    $rectorConfig->skip([
        __DIR__ . 'src/Eduprat/DomainBundle/Entity/EtutoratCasClinique.php',
        LongArrayToShortArrayRector::class,
//        Ann
        AddOverrideAttributeToOverriddenMethodsRector::class,
        ClosureToArrowFunctionRector::class,
        ClassOnThisVariableObjectRector::class,
        \Rector\Php73\Rector\FuncCall\JsonThrowOnErrorRector::class,
        \Rector\Php81\Rector\FuncCall\NullToStrictStringFuncCallArgRector::class,
//        \Rector\Php81\Rector\ClassConst\FinalizePublicClassConstantRector::class,
        \Rector\Php55\Rector\String_\StringClassNameToClassConstantRector::class,
        Rector\Php81\Rector\Array_\FirstClassCallableRector::class => [
            __DIR__ . 'src/Eduprat/DomainBundle/Entity/EtutoratCasClinique.php',
        ],
    ]);

};
