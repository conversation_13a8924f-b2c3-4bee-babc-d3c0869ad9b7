<?php

// Script pour analyser les routes et les rôles d'accès - Version détaillée
$controllers = [
    'src/Eduprat/AdminBundle/Controller',
    'src/Eduprat/AuditBundle/Controller',
    'src/Eduprat/ApiBundle/Controller',
    'src/Eduprat/CrmBundle/Controller',
    'src/Eduprat/PdfBundle/Controller',
    'src/Eduprat/DomainBundle/Controller'
];

$routes = [];

function extractRouteInfo($file) {
    $content = file_get_contents($file);
    $routes = [];

    // Extraire les annotations de classe
    preg_match('/class\s+(\w+).*?{/s', $content, $classMatch);
    $className = $classMatch[1] ?? 'Unknown';

    // Extraire les annotations de sécurité au niveau de la classe
    preg_match('/#\[IsGranted\([\'"]([^\'"]+)[\'"]\)\]\s*class/s', $content, $classSecurityMatch);
    $classRole = $classSecurityMatch[1] ?? null;

    // Extraire le préfixe de route de la classe
    preg_match('/#\[Route\(path:\s*[\'"]([^\'"]+)[\'"]\)\]\s*(?:#\[IsGranted.*?\]\s*)?class/s', $content, $classRouteMatch);
    $classPrefix = $classRouteMatch[1] ?? '';

    // Extraire toutes les méthodes avec leurs routes et méthodes HTTP
    preg_match_all('/#\[Route\((.*?)\)\]\s*(?:#\[IsGranted\([\'"]([^\'"]+)[\'"]\)\]\s*)?public\s+function\s+(\w+)/s', $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $routeParams = $match[1];
        $methodRole = $match[2] ?? null;
        $methodName = $match[3];

        // Extraire les paramètres de la route
        preg_match('/path:\s*[\'"]([^\'"]+)[\'"]/', $routeParams, $pathMatch);
        preg_match('/name:\s*[\'"]([^\'"]+)[\'"]/', $routeParams, $nameMatch);
        preg_match('/methods:\s*\[([^\]]+)\]/', $routeParams, $methodsMatch);

        $path = $pathMatch[1] ?? '';
        $routeName = $nameMatch[1] ?? 'unknown_route';
        $httpMethods = $methodsMatch[1] ?? 'ANY';

        // Nettoyer les méthodes HTTP
        $httpMethods = str_replace(['"', "'", ' '], '', $httpMethods);

        $fullPath = $classPrefix . $path;
        $role = $methodRole ?: $classRole;

        $routes[] = [
            'name' => $routeName,
            'path' => $fullPath,
            'methods' => $httpMethods,
            'role' => $role,
            'controller' => $className,
            'method' => $methodName,
            'file' => basename($file)
        ];
    }

    return $routes;
}

// Parcourir tous les contrôleurs
foreach ($controllers as $dir) {
    if (is_dir($dir)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($iterator as $file) {
            if ($file->getExtension() === 'php' && strpos($file->getFilename(), 'Controller.php') !== false) {
                $fileRoutes = extractRouteInfo($file->getPathname());
                $routes = array_merge($routes, $fileRoutes);
            }
        }
    }
}

// Trier les routes par nom
usort($routes, function($a, $b) {
    return strcmp($a['name'], $b['name']);
});

// Afficher les résultats en format détaillé
echo "LISTE DÉTAILLÉE DES ROUTES ET RÔLES D'ACCÈS - EDUPRAT\n";
echo "====================================================\n\n";

foreach ($routes as $route) {
    echo "🔗 **" . $route['name'] . "**\n";
    echo "   📍 Chemin: " . $route['path'] . "\n";
    echo "   🌐 Méthodes HTTP: " . $route['methods'] . "\n";
    echo "   🔐 Rôle requis: " . ($route['role'] ?: 'AUCUN RÔLE SPÉCIFIQUE') . "\n";
    echo "   📁 Contrôleur: " . $route['controller'] . "::" . $route['method'] . "\n";
    echo "   📄 Fichier: " . $route['file'] . "\n";
    echo "\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "HIÉRARCHIE DES RÔLES (depuis security.yaml):\n";
echo str_repeat("=", 80) . "\n";
echo "ROLE_USER (base)\n";
echo "├── ROLE_FORMER\n";
echo "├── ROLE_COORDINATOR_LBI\n";
echo "│   └── ROLE_COORDINATOR\n";
echo "│       ├── ROLE_SUPERVISOR\n";
echo "│       │   └── ROLE_SUPERVISOR_FRANCE\n";
echo "│       └── ROLE_FORMER_PHARMACIE\n";
echo "└── ROLE_WEBMASTER (hérite de FORMER, COORDINATOR, SUPERVISOR, SUPERVISOR_FRANCE)\n";
echo "    └── ROLE_WEBMASTER_COMPTA\n";
echo "        └── ROLE_SUPER_ADMIN (+ ROLE_ALLOWED_TO_SWITCH)\n";

echo "\n" . str_repeat("=", 80) . "\n";
echo "ZONES D'ACCÈS PRINCIPALES:\n";
echo str_repeat("=", 80) . "\n";
echo "• /gestion/* - Zone Admin (ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE)\n";
echo "• /crm/* - Zone CRM (ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE)\n";
echo "• /pdf/* - Zone PDF (PUBLIC_ACCESS)\n";
echo "• /api/* - Zone API (PUBLIC_ACCESS pour certaines, IS_AUTHENTICATED_FULLY pour d'autres)\n";
echo "• / - Zone Audit/Front (IS_AUTHENTICATED_FULLY)\n";

?>
