<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Eduprat\AdminBundle\Entity\CoordinatorPerson;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\DomainBundle\DTO\DTORatioEtForfaitMoyenCRDashboard;
use Eduprat\DomainBundle\DTO\DTORatioEtForfaitMoyenCRDashboardLine;
use Eduprat\DomainBundle\DTO\DTORepartitionCRDashboard;
use Eduprat\DomainBundle\DTO\DTORepartitionCRDashboardLine;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPowerpoint;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationVfc;
use Eduprat\DomainBundle\Entity\FormationTcs;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\Speciality;

/**
 * ParticipationRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ParticipationRepository extends EntityRepository
{

    public function getQueryBuilder($formation)
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.formation = :formation')
            ->andWhere('p.archived = false')
            ->setParameter('formation', $formation)
            ;
    }

    public function countQueryBuilder($formation)
    {
        return $this->getQueryBuilder($formation)
            ->select('count(p)')
            ->getQuery()->getSingleScalarResult();
    }

    public function listing($formation, $page, $nombre){
        return $this->getQueryBuilder($formation)
            ->join('p.participant', 'pp')
            ->addOrderBy('pp.lastname', 'ASC')
            ->setFirstResult(($page-1) * $nombre)
            ->setMaxResults($nombre)
            ->getQuery()->getResult();
    }

    public function findByDate($property, \DateTime $date) {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->andWhere('f.' . $property . ' = :date')
            ->andWhere('p.archived = false')
            ->setParameter('date', $date->format('Y-m-d H:i:s'))
            ->andWhere($queryBuilder->expr()->isNotNull('pa.user'));

        $queryBuilder = $this->addTypeConstraints($queryBuilder);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findByFormationOpeningDate(\DateTime $date) {
        return $this->findByDate('openingDate', $date);
    }

    public function findByFormationClosingDate(\DateTime $date) {
        return $this->findByDate('closingDate', $date);
    }

    public function findByProgrammeStartDate(\DateTime $date, $hours = false, $includeElearning = false) {
        $clonedDateStart = clone $date;
        if (!$hours) {
            $clonedDateStart->setTime(0, 0, 0);
        }
        $clonedDateEnd = clone $clonedDateStart;
        if (!$hours) {
            $clonedDateEnd->modify('+ 1 day');
        } else {
            $clonedDateEnd->modify('+ 1 hour');
        }
        $clonedDateEnd->modify('- 1 second');
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
           ->innerJoin('p.participant', 'pa')
           ->innerJoin('pa.user', 'u')
           ->andWhere('f.startDate BETWEEN :start and :end')
           ->andWhere('p.archived = false')
           ->andWhere('u.isArchived = false')
           ->andWhere('f.archived = false')
           ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
           ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'))
           ->andWhere($qb->expr()->isNotNull('pa.user'));

        $qb = $this->addTypeConstraints($qb, $includeElearning);

        return $qb->getQuery()->getResult();
    }

    public function findByDateAndClass($dateField, \DateTime $date, $classes, $hours = false, $ignoreArchived = false) {
        $clonedDateStart = clone $date;
        if (!$hours) {
            $clonedDateStart->setTime(0, 0, 0);
        }
        $clonedDateEnd = clone $clonedDateStart;
        if (!$hours) {
            $clonedDateEnd->modify('+ 1 day');
        } else {
            $clonedDateEnd->modify('+ 1 hour');
        }
        $clonedDateEnd->modify('- 1 second');

        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->andWhere(sprintf('%s BETWEEN :start and :end', $dateField))
            ->andWhere('p.archived = false')
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'))
            ->andWhere($qb->expr()->isNotNull('pa.user'));

        if ($ignoreArchived) {
            $qb
                ->innerJoin('pa.user', 'u')
                ->andWhere('u.isArchived = false')
                ->andWhere('f.archived = false');
        }

        self::byClass($qb, $classes);
        return $qb->getQuery()->getResult();
    }

    public function findByUnityOpeningDate(\DateTime $openingDate, $classes, $ignoreArchivedFormation = false) {
        $clonedDateStart = clone $openingDate;
        $clonedDateStart->setTime(0, 0, 0);
        $clonedDateEnd = clone $clonedDateStart;
        $clonedDateEnd->modify('+ 1 day');
        $clonedDateEnd->modify('+ 1 hour');
        $clonedDateEnd->modify('- 1 second');

        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->innerJoin('f.unities', 'u')
            ->andWhere('u.openingDate BETWEEN :start and :end')
            ->andWhere('p.archived = false')
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'));
        if ($ignoreArchivedFormation) {
            $qb->andWhere('f.archived = false');
        }

        self::byClass($qb, $classes);

        return $qb->getQuery()->getResult();
    }

    /**
     * formOpeningDate = null && startDate = defaultDate || formOpeningDate = today
     * @param \DateTime $defaultDate
     * @return array
     */
    public function findByFormOpeningDate(\DateTime $defaultDate, $ignoreFormationArchived = false) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa');

        $qb->andWhere($qb->expr()->orX(
            $qb->expr()->andX(
                $qb->expr()->eq('f.startDate', ':defaultDate'),
                $qb->expr()->isNull('f.formOpeningDate')
            ),
            $qb->expr()->eq('f.formOpeningDate', ':today')
        ))
            ->setParameter('defaultDate', $defaultDate->format('Y-m-d'))
            ->setParameter('today', (new \DateTime())->format('Y-m-d'))
            ->andWhere('p.archived = false')
            ->andWhere($qb->expr()->isNotNull('pa.user'));
        if ($ignoreFormationArchived) {
            $qb->andWhere('f.archived = false');
        }
        return $qb->getQuery()->getResult();
    }

    /**
     * formClosingDate = null && closingDate = defaultDate || formClosingDate = today + 5 days
     * @param \DateTime $defaultDate
     * @param int       $days Nombre de jours avant formClosingDate à sélectionner
     * @return array
     */
    public function findByFormClosingDate(\DateTime $defaultDate, $days = 5) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa');

        $qb = $this->addTypeConstraints($qb);

        $qb->andWhere($qb->expr()->orX(
            $qb->expr()->andX(
                $qb->expr()->eq('f.closingDate', ':defaultDate'),
                $qb->expr()->isNull('f.formClosingDate')
            ),
            $qb->expr()->eq('f.formClosingDate', ':today')
        ))
            ->setParameter('defaultDate', $defaultDate->format('Y-m-d'))
            ->setParameter('today', (new \DateTime())->modify("+ $days days")->format('Y-m-d'))
            ->andWhere('p.archived = false')
            ->andWhere($qb->expr()->isNotNull('pa.user'));

        return $qb->getQuery()->getResult();
    }

    public static function addTypeConstraints(QueryBuilder $qb, $includeElearning = false) {
        $classes = array(
            FormationVignetteAudit::class,
            FormationAudit::class,
            FormationPresentielle::class,
            FormationVignette::class,
            FormationVfc::class,
            FormationTcs::class,
        );
        $includeElearning ? $classes[] = FormationElearning::class : false;
        self::byClass($qb, $classes);
        return $qb;
    }

    /**
     * @param \DateTime $closingDate
     * @return array
     */
    public function findByFormClosingDateElearning(\DateTime $closingDate) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa');

        $qb->andWhere($qb->expr()->orX(
            $qb->expr()->isInstanceOf('f', FormationElearning::class),
            $qb->expr()->isInstanceOf('f', FormationPowerpoint::class),
            $qb->expr()->isInstanceOf('f', FormationVfc::class),
        ));

        $qb->andWhere(
            $qb->expr()->between('f.formClosingDate', ':start', ':end')
        );

        $qb->setParameter('start', $closingDate->format('Y-m-d'))
            ->setParameter('end',$closingDate->modify("+ 1 days")->format('Y-m-d'))
            ->andWhere('p.archived = false')
            ->andWhere($qb->expr()->isNotNull('pa.user'))
            ->andWhere('f.archived = false')
        ;

        return $qb->getQuery()->getResult();
    }

    public function findComplex($year = null, $medecins = null, array $options = [], array $classes = []) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f');
        $qb->innerJoin('p.participant', 'pa');
        if (!is_null($medecins)) {
            $this->includeMedecins($qb, $medecins);
        }
        if (sizeof($classes)) {
            self::byClass($qb, $classes);
        }
        if (!is_null($year)) {
            $this->byYear($qb, $year);
        }
        if (sizeof($options)) {
            $this->byStatus($qb, $options);
        }
        $qb->andWhere('p.archived = false');
        return $qb->getQuery()->getResult();
    }

    public function includeMedecins(QueryBuilder $qb, $include) {
        if ($include) {
            $qb->andWhere('pa.category = :category');
        } else {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->neq('pa.category', ':category'),
                $qb->expr()->isNull('pa.category')
            ));
        }
        $qb->setParameter('category', Participant::CATEGORY_MEDECIN);
    }

    static public function byClass(QueryBuilder $qb, array $classes = []) {
        $subType = false;
        foreach ($classes as $class) {
            if ($class === FormationAudit::TYPE_CLASSIC || $class === FormationAudit::TYPE_PREDEFINED) {
                $qb->leftJoin(FormationAudit::class, 'wffa', 'WITH', 'wffa.id = f.id');
                $qb->leftJoin(Audit::class, 'au', 'WITH', 'au.id = wffa.audit');
                $qb->andWhere("au.type LIKE :auditType")->setParameter('auditType', $class === FormationAudit::TYPE_CLASSIC ? "default" : $class);
                $subType = true;
            }
        }

        if (!$subType) {
            $ors = array_map(function($class) use ($qb) {
                return $qb->expr()->isInstanceOf('f', $class);
            }, $classes);
            $qb->andWhere(call_user_func_array(array($qb->expr(), "orX"), $ors));

            if (in_array(FormationPresentielle::class, $classes)) {
                $allClasses = array_diff(array_values(Formation::TYPES), $classes);
                if (count($allClasses) > 0) {
                    $ors = array_map(function($class) use ($qb) {
                        return $qb->expr()->isInstanceOf('f', $class);
                    }, $allClasses);
                    $qb->andWhere($qb->expr()->not(call_user_func_array(array($qb->expr(), "orX"), $ors)));
                }
            }
        }


    }

    public function byCriteria(QueryBuilder $qb, array $criterias = []) {
        foreach ($criterias as $key => $value) {
            $k = str_replace(".", "", $key);
            $qb->andWhere($qb->expr()->eq($key, ":$k"))->setParameter($k, $value);
        }
    }

    public function byYear(QueryBuilder $qb, $year = null) {
        if (!is_null($year)) {
            $start = new \DateTime('first day of January ' . $year);
            $end = clone $start;
            $end->modify('+ 1 year');
            $qb->andWhere('f.startDate BETWEEN :start AND :end')
                ->setParameter('start', $start->format('Y-m-d'))
                ->setParameter('end', $end->format('Y-m-d'));
        }
    }

    public function byStatus(QueryBuilder $qb, $statuses) {
        foreach ($statuses as $status => $value) {
            switch ($status) {
                case Formation::STATUS_CLOSED:
                    $qb->andWhere('f.closed = :closed')->setParameter('closed', $value);
                    break;
                case Formation::STATUS_OPENED:
                    $qb->andWhere('f.openingDate < :now')
                        ->andWhere('f.closed = :closed')->setParameter('closed', false)
                        ->setParameter('now', (new \DateTime())->format('Y-m-d'));
                    break;
                case Formation::STATUS_FUTURE:
                    $qb->andWhere('f.openingDate > :now')->setParameter('now', (new \DateTime())->format('Y-m-d'))
                        ->andWhere('f.closed = :closed')->setParameter('closed', false);
                    break;
            }
        }
    }

    public function findByCoordinatorsTotalMedecin($start, $end) {
        $qb = $this->createQueryBuilder('p')
            ->innerJoin('p.formation', 'f')
            ->select('p.id')
            ->andWhere('p.price = 760')
            ->andWhere('f.querySearchStartDate BETWEEN :start AND :end')
            ->andWhere('p.archived = false')
            ->setParameter('start', $start->format('Y-m-d'))
            ->setParameter('end', $end->format('Y-m-d'));
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function findBySupervisorsTotalMedecin() {
        $qb = $this->createQueryBuilder('p')
            ->select('p.id')
            ->andWhere('p.price = 760')
            ->andWhere('p.archived = false');
        return $qb->getQuery()->getScalarResult();
    }

    public function findByBatch($formation, $financeSousMode, $start, $batch) {
        $qb = $this->createQueryBuilder('p')
            ->andWhere('p.formation = :formation')
            ->andWhere('p.financeSousMode = :financeSousMode')
            ->andWhere('p.archived = false')
            ->setFirstResult($start)
            ->setMaxResults($batch)
            ->innerJoin('p.participant', 'pa')
            ->orderBy('pa.lastname', 'ASC')
            ->setParameter('formation', $formation)
            ->setParameter('financeSousMode', $financeSousMode);

        return $qb->getQuery()->getResult();
    }

    public function findByPersonProgramme(Programme $programme, Person $person) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('f.programme', 'pg')
            ->innerJoin('p.participant', 'pa')
            ->innerJoin('pa.user', 'u')
            ->andWhere('p.archived = false')
            ->andWhere('pg.id = :programme')->setParameter('programme', $programme->getId())
            ->andWhere('u.id = :person')->setParameter('person', $person->getId())
        ;
        return $qb->getQuery()->getResult();
    }

    public function findByPersonFormation(Formation $formation, Person $person) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->innerJoin('pa.user', 'u')
            ->andWhere('p.archived = false')
            ->andWhere('f.id = :formation')->setParameter('formation', $formation->getId())
            ->andWhere('u.id = :person')->setParameter('person', $person->getId())
        ;
        return $qb->getQuery()->getResult();
    }

    public function findByFormation(Formation $formation) {
        $qb = $this->createQueryBuilder('p')->select('p');
        if ($formation->isAudit() || ($formation->isElearning() && $formation->getFormType() != "survey")) {
            $qb->innerJoin('p.auditAnswers', 'aa')->addSelect('aa');
        } else if ($formation->isPresentielle()) {
            $qb->innerJoin('p.surveyAnswers', 'sa')->addSelect('sa');
        }
        $qb->where('p.formation = :id')->setParameter('id', $formation->getId());
        $qb->andWhere('p.archived = false');
        return $qb->getQuery()->getResult();
    }

    public function findByFormationPerson(Formation $formation, Person $person) {
        $qb = $this->createQueryBuilder('p')->select('p');
        $qb->innerJoin('p.participant', 'pa');
        if ($formation->isAudit()) {
            $qb->leftJoin('p.auditAnswers', 'aa')->addSelect('aa');
        } else if ($formation->isPresentielle()) {
            $qb->leftJoin('p.surveyAnswers', 'sa')->addSelect('sa');
        }
        $qb->where('p.formation = :id')->setParameter('id', $formation->getId());
        $qb->andWhere('pa.user = :person')->setParameter('person', $person->getId());
        $qb->andWhere('p.archived = false');
        return $qb->getQuery()->getResult();
    }

    public function findWithJoins($id) {
        $qb = $this->createQueryBuilder('p')->select('p, pa, f, i, sa, aa, pd');
        $qb->innerJoin('p.formation', 'f')
            ->leftJoin('f.invoices', 'i')
            ->leftJoin('f.programme', 'pa')
            ->leftJoin('p.surveyAnswers', 'sa')
            ->leftJoin('p.auditAnswers', 'aa')
            ->leftJoin('p.patientsDescriptions', 'pd')
            ->where('p.id = :id')->setParameter('id', $id)
            ->andWhere('p.archived = false')
        ;
        return $qb->getQuery()->getSingleResult();
    }

    public function findByYearForParticipant(Participant $participant, $year)
    {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f');
        $qb->innerJoin('p.participant', 'pa');
        if (!is_null($year)) {
            $this->byYear($qb, $year);
        }
        $qb->andWhere('pa.id = :id')->setParameter('id', $participant->getId());
        $qb->andWhere('p.archived = false');
        $qb->orderBy('f.openingDate', 'DESC');
        $qb->addOrderBy('f.createdAt', 'DESC');
        return $qb->getQuery()->getResult();
    }

    public function findCountsForSupervisor(Person $person, $year) {
        $supervisorId = $person->getId();
        $select = 'p.id as pCount, pa.id as paCount, p.category';
        $qb = $this->createQueryBuilder('pa')->select($select);
        $qb->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.coordinators', 'c')
            ->innerJoin('c.person', 'pe')
            ->andWhere('pa.archived = false')
            ->andWhere("pa.coordinator IS NULL");
        if ($person->isSupervisor()) {
            $qb->andWhere("pe.supervisor = :id")
                ->setParameter("id", $supervisorId);
        }

        $this->byYear($qb, $year);
        $r1 = $qb->getQuery()->getScalarResult();

        $qb = $this->createQueryBuilder('pa')->select($select);
        $qb->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('pa.coordinator', 'c')
            ->innerJoin('c.person', 'pe')
            ->andWhere('pa.archived = false');
        if ($person->isSupervisor()) {
            $qb->andWhere("pe.supervisor = :id")
                ->setParameter("id", $supervisorId);
        }
        $this->byYear($qb, $year);
        $r2 = $qb->getQuery()->getScalarResult();

        $results = array();

        foreach (array($r1, $r2) as $result) {
            foreach ($result as $item) {
                if (!isset($results[$item["category"]])) {
                    $results[$item["category"]] = array(
                        "participations" => array(),
                        "participants" => array(),
                    );
                }
                $results[$item["category"]]["participations"][] = $item["paCount"];
                $results[$item["category"]]["participants"][] = $item["pCount"];
            }
        }

        $totals =  array(
            "participations" => 0,
            "participants" => 0,
        );

        foreach ($results as $key => $result) {
            $results[$key]["participations"] = count(array_unique($results[$key]["participations"]));
            $results[$key]["participants"] = count(array_unique($results[$key]["participants"]));
            $totals["participations"] += $results[$key]["participations"];
            $totals["participants"] += $results[$key]["participants"];
        }

        return array("totals" => $totals, "categories" => $results);
    }

    public function findCountsForPerson($personId, $year) {
        $select = 'p.id as pCount, pa.id as paCount, p.category';
        $qb = $this->createQueryBuilder('pa')->select($select);
        $qb->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.coordinators', 'c')
            ->innerJoin('c.person', 'pe')
            ->andWhere('pa.archived = false')
            ->andWhere("pe.id = :id")
            ->andWhere("pa.coordinator IS NULL")
            ->setParameter("id", $personId);
        $this->byYear($qb, $year);
        $r1 = $qb->getQuery()->getScalarResult();

        $qb = $this->createQueryBuilder('pa')->select($select);
        $qb->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('pa.coordinator', 'c')
            ->innerJoin('c.person', 'pe')
            ->andWhere('pa.archived = false')
            ->andWhere("pe.id = :id")
            ->setParameter("id", $personId);
        $this->byYear($qb, $year);
        $r2 = $qb->getQuery()->getScalarResult();

        $results = array();

        foreach (array($r1, $r2) as $result) {
            foreach ($result as $item) {
                if (!isset($results[$item["category"]])) {
                    $results[$item["category"]] = array(
                        "participations" => array(),
                        "participants" => array(),
                    );
                }
                $results[$item["category"]]["participations"][] = $item["paCount"];
                $results[$item["category"]]["participants"][] = $item["pCount"];
            }
        }

        $totals =  array(
            "participations" => 0,
            "participants" => 0,
        );

        foreach ($results as $key => $result) {
            $results[$key]["participations"] = count(array_unique($results[$key]["participations"]));
            $results[$key]["participants"] = count(array_unique($results[$key]["participants"]));
            $totals["participations"] += $results[$key]["participations"];
            $totals["participants"] += $results[$key]["participants"];
        }

        return array("totals" => $totals, "categories" => $results);
    }

    public function findByParticipantNameAndFormation($firstname, $lastname, Formation $formation)
    {
        $qb = $this->createQueryBuilder('pa');
        $qb->innerJoin('pa.formation', 'f');
        $qb->innerJoin('pa.participant', 'p');
        $qb->where('f.id = :id')->setParameter('id', $formation->getId());
        $qb->andWhere('p.firstname = :firstname')->setParameter('firstname', $firstname);
        $qb->andWhere('p.lastname = :lastname')->setParameter('lastname', $lastname);
        $qb->andWhere('pa.archived = false');
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function countByCategoryProspect($year = null): array
    {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('p.category, count(DISTINCT pa.id) as participation, count(DISTINCT p.id) as participant')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->andWhere('p.category is not null')
            ->addGroupBy("p.category")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countByExercisesMode($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('pr.exercisesMode, count(DISTINCT pa.id) as participation, count(DISTINCT p.id) as participant')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.programme', 'pr')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->groupBy("pr.exercisesMode")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countByMethod($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('pr.method, p.category, count(DISTINCT pa.id) as participation')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.programme', 'pr')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->andWhere('p.category is not null')
            ->andWhere('pr.method is not null')
            ->groupBy("pr.method")
            ->addGroupBy("p.category")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countByFormat($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('p.category, pr.durationPresentielle, pr.durationNotPresentielle, pr.durationNotPresentielleActalians, count(DISTINCT pa.id) as participation')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.programme', 'pr')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->andWhere('p.category is not null')
            ->groupBy("p.category, pr.durationPresentielle, pr.durationNotPresentielle, pr.durationNotPresentielleActalians")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countByProgrammeCategories($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('p.category, pr.categories, count(DISTINCT pa.id) as participation')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.programme', 'pr')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->andWhere('p.category is not null')
            ->groupBy("p.category, pr.categories")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countCaByCategory($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select('p.category, SUM(pa.price) as total')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->andWhere('p.category is not null')
            ->addGroupBy("p.category")
        ;
        return $qb->getQuery()->getScalarResult();
    }

    public function countCrmAnalysis($year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        return $this->createQueryBuilder('pa')
            ->select('pa.id as participation, p.id as participant, f.id as session, f.startDate, pa.exerciseMode, pr.presence, p.category, c.id as coordinator, p.uga')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin("pa.formation", "f")
            ->innerJoin("p.coordinator", "c")
            ->innerJoin("f.programme", "pr")
            ->andWhere("f.startDate between :start and :end")
            ->andWhere("pa.archived = false")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->addGroupBy("pa.id")
            ->getQuery()
            ->getScalarResult();
    }

    public function findWithoutCoordinator(\DateTime $start, \DateTime $end)
    {
        $qb = $this->createQueryBuilder('pa');
        $qb->select("pa, (SELECT count(c2) from " . Coordinator::class . " c2 where c2.formation = f.id) as HIDDEN nbcoord");
        $qb->innerJoin('pa.formation', 'f');
        $qb->innerJoin('f.programme', 'pr');
        $qb->innerJoin('pa.participant', 'p');
        $qb->leftJoin("pa.coordinator", "c");
        $qb->leftJoin("c.person", "cpe");
        $qb
            ->where('pa.coordinator is null')
            ->orWhere("cpe.lastname IN (:forbiddenCoordinators)")
            ->andWhere('f.querySearchStartDate BETWEEN :start and :end')
            ->andWhere('pa.archived = false')
            ->setParameter("forbiddenCoordinators", array("E-LEARNING", "CLASSES VIRTUELLES"))
            ->setParameter('start', $start->format('Y-m-d H:i:s'))
            ->setParameter('end', $end->format('Y-m-d H:i:s'));
        $qb->andHaving("nbcoord > 1");
        return $qb->getQuery()->getResult();
    }

    public function findWithOneCoordinator()
    {
        $qb = $this->createQueryBuilder('pa');
        $qb->select("pa, (SELECT count(c2) from " . Coordinator::class . " c2 where c2.formation = f.id) as HIDDEN nbcoord");
        $qb->innerJoin('pa.formation', 'f');
        $qb->andWhere('pa.coordinator is null');
        $qb->andHaving("nbcoord = 1");
        return $qb->getQuery()->getResult();
    }

    public function findMissingForm(ComptabiliteSearch $search, $page = 1, $number = 50) {

        $start = (new \DateTime())->format("Y-m-d");

        // Questionnaire pré Elearning : non rempli après date d'ouverture - échéance = date de cloture
        $qb1 = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'form1' as formType, f.closingDate as echeance, 0 as position")
            ->where("pa.archived = false")
            ->andWhere("pa.completedForm1 = false")
            ->andWhere("f.openingDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->orderBy("f.startDate", "asc")
        ;

        self::byClass($qb1, array(FormationElearning::class));

        // Questionnaire pré hors Elearning : non rempli après date d'ouverture - échéance = date de réunion
        $qb2 = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'form1' as formType, f.startDate as echeance, 2 as position")
            ->where("pa.archived = false")
            ->andWhere("pa.completedForm1 = false")
            ->andWhere("f.openingDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->orderBy("f.startDate", "asc")
        ;

        self::byClass($qb2, array_diff(array_values(Formation::TYPES), array(FormationElearning::class)));

        // Questionnaire post hors Elearning : non rempli après date de réunion - échéance = date de cloture
        $qb3 = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'form2' as formType, f.closingDate as echeance, 2 as position")
            ->where("pa.archived = false")
            ->andWhere("pa.completedForm2 = false")
            ->andWhere("f.startDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->orderBy("f.closingDate", "asc")
        ;
        self::byClass($qb3, array_diff(array_values(Formation::TYPES), array(FormationElearning::class)));

        // Questionnaire post Elearning : non rempli après plateforme elearning rempli - échéance = date de cloture
        $qb4 = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'form2' as formType, f.closingDate as echeance, 2 as position")
            ->where("pa.archived = false")
            ->andWhere("pa.elearningPhaseDone = true")
            ->andWhere("pa.completedForm2 = false")
            ->andWhere("f.openingDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->orderBy("f.closingDate", "asc")
        ;
        self::byClass($qb4, array(FormationElearning::class));

        // Phase Elearning : non rempli après date d'ouverture - échéance = date de cloture
        $qb5 = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'elearning' as formType, f.closingDate as echeance, 1 as position")
            ->where("pa.archived = false")
            ->andWhere("pa.elearningPhaseDone = false")
            ->andWhere("f.openingDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->orderBy("f.closingDate", "asc")
        ;

        self::byClass($qb5, array(FormationElearning::class));

        $qbs = array($qb1, $qb2, $qb3, $qb4, $qb5);

        foreach ($qbs as $qb) {
            if ($search->start) {
                $qb->andWhere($qb->expr()->gte('f.startDate', ':startDate'))->setParameter('startDate', $search->start);
            }

            if ($search->end) {
                $search->end->setTime(23, 59, 59);
                $qb->andWhere($qb->expr()->lte('f.endDate', ':endDate'))->setParameter(':endDate', $search->end);
            }
        }

        if ($search->coordinator) {
            foreach ($qbs as $qb) {
                $qb
                    ->innerJoin("f.coordinators", "c")
                    ->innerJoin("c.person", "pe")
                    ->leftJoin("pa.coordinator", "pac")
                    ->leftJoin("pac.person", "pec")
                    ->andWhere("c.person = :id1")->setParameter("id1", $search->coordinator->getId())
                    ->andWhere(
                        $qb->expr()->orX(
                            $qb->expr()->isNull("pa.coordinator"),
                            $qb->expr()->andX(
                                $qb->expr()->isNotNull("pa.coordinator"),
                                $qb->expr()->eq("pec.id", $search->coordinator->getId()),
                            ),
                        )
                    )
                ;
            }
        }

        if ($search->supervisor) {
            foreach ($qbs as $qb) {
                $qb
                    ->leftJoin("f.coordinators", "cs")
                    ->leftJoin("cs.person", "pes")
                    ->leftJoin("pa.coordinator", "pacs")
                    ->leftJoin("pacs.person", "pecs")
                    ->andWhere("pes.supervisor = :supervisor")->setParameter("supervisor", $search->supervisor->getId())
                    ->andWhere(
                        $qb->expr()->orX(
                            $qb->expr()->isNull("pa.coordinator"),
                            $qb->expr()->andX(
                                $qb->expr()->isNotNull("pa.coordinator"),
                                $qb->expr()->eq("pecs.supervisor", $search->supervisor->getId()),
                            ),
                        )
                    )
                ;
            }
        }

        if ($search->webmaster) {
            foreach ($qbs as $qb) {
                $qb
                    ->leftJoin("f.coordinators", "cw")
                    ->leftJoin("cw.person", "pew")
                    ->leftJoin("pa.coordinator", "pacw")
                    ->leftJoin("pacw.person", "pecw")
                    ->andWhere("pew.webmaster = :webmaster")->setParameter("webmaster", $search->webmaster->getId())
                    ->andWhere(
                        $qb->expr()->orX(
                            $qb->expr()->isNull("pa.coordinator"),
                            $qb->expr()->andX(
                                $qb->expr()->isNotNull("pa.coordinator"),
                                $qb->expr()->eq("pecw.webmaster", $search->webmaster->getId()),
                            ),
                        )
                    )
                ;
            }
        }

        $result = array_merge(
          $qb1->getQuery()->getResult(),
          $qb2->getQuery()->getResult(),
          $qb3->getQuery()->getResult(),
          $qb4->getQuery()->getResult(),
          $qb5->getQuery()->getResult(),
        );

        $groupedResult = array();

        foreach ($result as $key => $item) {
            if (!$item["participation"]->getFormation()->hasLinkedForm()) {
                continue;
            }
            if (!isset($groupedResult[$item['participation']->getId()]) || (int)$item["position"] < (int)$groupedResult[$item['participation']->getId()]["position"]) {
                $groupedResult[$item['participation']->getId()] = $item;
            }
        }

        ksort($groupedResult, SORT_NUMERIC);

        $offset = ($page - 1) * $number;
        $offset = $offset > 0 ? $offset : 0;

        usort($groupedResult, function($a, $b) {
            return $a["echeance"]->getTimestamp() - $b["echeance"]->getTimestamp();
        });

        return array(
            "count" => count($groupedResult),
            "result" => array_slice(array_values($groupedResult), $offset, $number),
        );
    }

    public function countMissingModules(ComptabiliteSearch $search): int
    {
        return $this->qbMissingModules($search)
            ->select('COUNT(pa.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findMissingModules(ComptabiliteSearch $search, ?int $offset = null, ?int $number = null): array
    {
        return $this->qbMissingModules($search, $offset, $number)
            ->getQuery()
            ->getResult();
    }

    private function qbMissingModules(ComptabiliteSearch $search, ?int $offset = null, ?int $number = null)
    {
        $start = (new \DateTime())->format("Y-m-d");

        $qb = $this->createQueryBuilder('pa')
            ->innerJoin("pa.formation", "f")
            ->select("pa as participation, 'form1' as formType, pa.dateModuleManquantExpiration as dateModuleManquantExpiration")
            ->where("pa.archived = false")
            ->andWhere("pa.nextModule IS NOT NULL")
            ->andWhere("f.openingDate <= :start")
            ->setParameter("start", $start)
            ->andWhere("f.closed = false")
            ->addOrderBy("pa.dateModuleManquantExpiration", Order::Ascending->value)
            ->addOrderBy("f.startDate", "asc")
        ;
        if (null !== $offset) {
            $qb->setFirstResult($offset);
        }
        if (null != $number) {
            $qb->setMaxResults($number);
        }

        if ($search->querySearchStartDate) {
            $qb->andWhere($qb->expr()->gte('f.querySearchStartDate', ':startDate'))->setParameter('startDate', $search->querySearchStartDate);
        }

        if ($search->querySearchEndDate) {
            $search->querySearchEndDate->setTime(23, 59, 59);
            $qb->andWhere($qb->expr()->lte('f.querySearchEndDate', ':endDate'))->setParameter(':endDate', $search->querySearchEndDate);
        }

        if ($search->coordinator) {
            $qb
                ->innerJoin("f.coordinators", "c")
                ->innerJoin("c.person", "pe")
                ->leftJoin("pa.coordinator", "pac")
                ->leftJoin("pac.person", "pec")
                ->andWhere("c.person = :id1")->setParameter("id1", $search->coordinator->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                            $qb->expr()->eq("pec.id", $search->coordinator->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->supervisor) {
            $qb
                ->leftJoin("f.coordinators", "cs")
                ->leftJoin("cs.person", "pes")
                ->leftJoin("pa.coordinator", "pacs")
                ->leftJoin("pacs.person", "pecs")
                ->andWhere("pes.supervisor = :supervisor")->setParameter("supervisor", $search->supervisor->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                                $qb->expr()->eq("pecs.supervisor", $search->supervisor->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->webmaster) {
            $qb
                ->leftJoin("f.coordinators", "cw")
                ->leftJoin("cw.person", "pew")
                ->leftJoin("pa.coordinator", "pacw")
                ->leftJoin("pacw.person", "pecw")
                ->andWhere("pew.webmaster = :webmaster")->setParameter("webmaster", $search->webmaster->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                            $qb->expr()->eq("pecw.webmaster", $search->webmaster->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->parcoursMandatory) {
            $qb->innerJoin('pa.financeSousMode', 'fsmf');
            if($search->parcoursMandatory == "oui") {
                $qb->andWhere('fsmf.priseEnCharge NOT IN (:values)')->setParameter('values', [FinanceSousMode::PARCOURS_HORS_DPC, FinanceSousMode::FAF_PM]);
            } else {
                $qb->andWhere('fsmf.priseEnCharge IN (:values)')->setParameter('values', [FinanceSousMode::PARCOURS_HORS_DPC, FinanceSousMode::FAF_PM]);
            }
        }
        FormationRepository::withForm($qb);

        return $qb;
    }

    /**
     * Les documents sont considérés comme manquants dès que la date de réunion est dépassée.
     * Pour les e-learning, à J+15 de la date d’ouverture.
     * @param Person|null $coordinator
     * @return QueryBuilder
     */
    public function getMissingAttestationFilesQuery(AttestationComptabiliteSearch $search, $parcoursV3MigrationDate) {
        $tomorrow = (new \DateTime("tomorrow"))->setTime(0, 0)->format("Y-m-d");
        $startElearning = (new \DateTime())->modify("- 15 days")->format("Y-m-d");

        $qb = $this->createQueryBuilder("prt")
            ->select("DISTINCT prt")
            ->innerJoin("prt.formation", "f")
            ->innerJoin("f.programme", "p")
            ->innerJoin("p.unities", "pu")
            ->innerJoin("prt.financeSousMode", "fsm")
            ->innerJoin("fsm.financeMode", "fm")
            ->leftJoin("prt.coordinator", "c")
            ->leftJoin("c.person", "pe")
            ->leftJoin("prt.participant", "pa")
            ->andWhere("prt.archived = false")
            ->andWhere("f.billed = false")
            ->andWhere("pu.nbHoursOffline > 0")
            ->andWhere("fm.name LIKE :dpc")
            ->andWhere("f.openingDate >= :migration")
            ->orderBy("f.attestationMissingDate", "ASC")
            ->addOrderBy("p.reference", "ASC")
            ->addOrderBy("f.sessionNumber", "ASC")
            ->addOrderBy("pa.lastname", "ASC")
        ;

        $qb->setParameter('dpc', FinanceMode::DPC_NAME);
        $qb->setParameter('migration', "2021-11-01");

        if ($search->querySearchStartDate) {
            $qb->andWhere($qb->expr()->gte('f.querySearchStartDate', ':startDate'))->setParameter('startDate', $search->querySearchStartDate);
        }

        if ($search->querySearchEndDate) {
            $search->querySearchEndDate->setTime(23, 59, 59);
            $qb->andWhere($qb->expr()->lte('f.querySearchEndDate', ':endDate'))->setParameter(':endDate', $search->querySearchEndDate);
        }

        if ($search->coordinator) {
            $qb
                ->andWhere("c.person = :coordinator")
                ->setParameter("coordinator", $search->coordinator->getId());
        }

        if ($search->supervisor) {
            $qb
                ->andWhere("pe.supervisor = :supervisor")
                ->setParameter("supervisor", $search->supervisor->getId());
        }

        if ($search->webmaster) {
            $qb
                ->andWhere("pe.webmaster = :webmaster")
                ->setParameter("webmaster", $search->webmaster->getId());
        }

        $formats = array();
        if($search->e_learning) {
            $formats[] = Programme::PRESENCE_ELEARNING;
        }
        if($search->sur_site) {
            $formats[] = Programme::PRESENCE_SITE;
        }
        if($search->classe_virtuelle) {
            $formats[] = Programme::PRESENCE_VIRTUELLE;
        }
        if(count($formats)) {
            $qb->andWhere($qb->expr()->in('p.presence', $formats));
        }

        if ($search->courseEnded) {
            $qb->leftJoin("p.competences", "competences");
            $qb->leftJoin("p.connaissances", "connaissances");
            $likeMethod = $search->courseEnded === "oui" ? "like" : "notLike";
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->andX(
                    // Avant refonte parcours participant : questionnaire post complété
                        $qb->expr()->lt("f.openingDate", ":migrationDate"),
                        $qb->expr()->eq("prt.completedForms", ":completed"),
                        $qb->expr()->isNull("f.course"),
                    ),
                    $qb->expr()->orX(
                    // S'il y'a des compétences/connaissances, parcours complet lorsque fiche synthese complet
                        $qb->expr()->andX(
                            $qb->expr()->orX(
                                $qb->expr()->isNotNull('competences.id'),
                                $qb->expr()->isNotNull('connaissances.id')
                            ),
                            $qb->expr()->$likeMethod("prt.course", ":ficheSyntheseModule")
                        ),
                        $qb->expr()->andX(
                            $qb->expr()->isNull('competences.id'),
                            $qb->expr()->isNull('connaissances.id'),
                            $qb->expr()->$likeMethod("prt.course", ":endModule"),
                            $qb->expr()->isNull("f.course"),
                        )
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->isNotNull("f.course"),
                        $qb->expr()->eq("prt.courseEnded", ":completed")
                    )
                )
            );

            $qb->setParameter("completed", $search->courseEnded === "oui");
            $qb->setParameter("ficheSyntheseModule", "%" . CourseManager::STEP4_SYNTHESE_LABEL . "%");
            $qb->setParameter("endModule", "%" . CourseManager::STEP4_END_LABEL . "%");
            $qb->setParameter("migrationDate", $parcoursV3MigrationDate);
        }



        $qb->andWhere($qb->expr()->notIn('fsm.priseEnCharge', FinanceSousMode::attestationMandatory()));

        $now = (new \DateTime())->setTime(0, 0)->format("Y-m-d");
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->andX(
                    $qb->expr()->isNull('prt.attestationHonneur'),
                    $qb->expr()->lte("f.attestationMissingDate", ":now"),
                ),
                $qb->expr()->andX(
                    $qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                    $qb->expr()->isNotNull('f.attestationMissingDateN1'),
                    $qb->expr()->lte("f.attestationMissingDateN1", ":now"),
                    $qb->expr()->isNull('prt.attestationHonneurN1')
                )
            )
        )->setParameter("now", $now);

        return $qb;
    }

    public function countMissingAttestationFiles(AttestationComptabiliteSearch $search, $parcoursV3MigrationDate) {
        $queryBuilder = $this->getMissingAttestationFilesQuery($search, $parcoursV3MigrationDate)->select('COUNT(DISTINCT prt.id)');
        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function findMissingAttestationFiles(AttestationComptabiliteSearch $search, $parcoursV3MigrationDate, $page, $number) {
        return $this->getMissingAttestationFilesQuery($search, $parcoursV3MigrationDate)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)->getQuery()->getResult();
    }

    public function findByFormationNonPresentielleByClosingDate(\DateTime $date) {

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->innerJoin('f.programme', 'pr')
            ->andWhere('f.closingDate = :date')
            ->andWhere('p.archived = false')
            ->setParameter('date', $date->format('Y-m-d H:i:s'))
            ->andWhere($queryBuilder->expr()->isNotNull('pa.user'))
            ->andWhere('pr.format != :format')->setParameter("format", Programme::FORMAT_PRESENTIEL);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findByPeriodEtutoratComms($start, $end) {
        $qb = $this->createQueryBuilder('p');
        $qb->innerJoin('p.formation', 'f');

        $qb->where(
            $qb->expr()->orX(
                $qb->expr()->gte('f.querySearchStartDate', ':start')
            )
        );
        $qb->setParameter(':start', $start);

        $end->setTime(23, 59, 59);
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->lte('f.querySearchEndDate', ':end')
            )
        );
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->isNotNull('p.etutoratAttentes'),
                $qb->expr()->isNotNull('p.etutorat2Message'),
                $qb->expr()->isNotNull('p.etutorat3Message'),
            )
        );
        $qb->andWhere('p.archived = :archived')->setParameter('archived', false);
        $qb->setParameter(':end', $end);

        return $qb->getQuery()->getResult();
    }

    public function findPluriannuelleQB(\DateTime $endDate): QueryBuilder
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $endDate->setTime(0, 0, 0);
        $endDateFinJournee = new \DateTime($endDate);
        $endDateFinJournee->modify('+ 1 day - 1 seconde');

        $queryBuilder
            ->innerJoin('p.formation', 'f')
            ->andWhere('p.archived = false')
            ->andWhere('f.archived = false')
            ->andWhere('f.endDate BETWEEN :end_date AND :end_date_fin_journee')
            ->setParameter('end_date', $endDate)
            ->setParameter('end_date_fin_journee', $endDateFinJournee)
            ->andWhere('YEAR(f.openingDate) != YEAR(f.closingDate)')
        ;
        return $queryBuilder;
    }
    public function findPluriannuelle(\DateTime $endDate)
    {
        return $this->findPluriannuelleQB($endDate)->getQuery()->getResult();
    }

    public function findPluriannuelleWithoutAttestationHonneur(\DateTime $endDate)
    {
        return $this->findPluriannuelleQB($endDate)
            ->andWhere('p.attestationHonneur is null')
            ->getQuery()->getResult()
        ;
    }

    public function findLead(\DateTime $startDate, \DateTime $endDate, $gpmOnly = true) {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->innerJoin('p.formation', 'f')
            ->innerJoin('p.participant', 'pa')
            ->andWhere('p.archived = false')
            ->andWhere('f.archived = false')
            ->andWhere('pa.leadCreationDate BETWEEN :start and :end')
            ->andWhere('p.isLead = :isLead')
            ->orderBy('f.startDate', 'DESC')
            ->setParameter('isLead', true)
            ->setParameter('start', $startDate->format('Y-m-d H:i:s'))
            ->setParameter('end', $endDate->format('Y-m-d H:i:s'));

        if ($gpmOnly) {
            $queryBuilder->andWhere("p.partenariat = :partenariat")->setParameter("partenariat", Participant::PART_GPM);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function findParticicipationsForCoordinatorBetweenDates(Person $coordinator, \DateTime $startDate, \DateTime $endDate)
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
        ->select('COUNT(p.id)')
        ->innerJoin('p.coordinator', 'c')
        ->innerJoin('c.person', 'pe')
        ->where('pe.id = :personId')
        ->andWhere('p.createdAt >= :startDate')
        ->andWhere('p.createdAt <= :endDate')
        ->andWhere('p.archived = false')
        ->setParameter('personId', $coordinator->getId())
        ->setParameter('startDate', $startDate)
        ->setParameter('endDate', $endDate);

        return (int) $queryBuilder->getQuery()->getSingleScalarResult();
    }

    /**
     * Compte les participations sans coût pour un coordinateur sur des formations futures
     *
     * @param Person $coordinator Le coordinateur
     * @return int Nombre de participations sans coût
     */
    public function countInscriptionsWithoutCostsForCoordinator(Person $coordinator): int
    {
        $today = new \DateTime();
        $today->setTime(0, 0, 0);

        $qb = $this->createQueryBuilder('p');
        $qb
            ->innerJoin('p.formation', 'f')
            ->where('p.archived = false')
            ->andWhere('f.openingDate > :today')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->isNull('p.price'),
                    $qb->expr()->eq('p.price', 0)
                )
            )
            ->setParameter('today', $today);

        // Filtrer par coordinateur (soit directement sur la participation, soit via les coordinateurs de la formation)
        $qb->leftJoin('f.coordinators', 'c')
            ->leftJoin('c.person', 'cp')
            ->leftJoin('p.coordinator', 'pc')
            ->leftJoin('pc.person', 'pcp')
            ->andWhere(
                $qb->expr()->orX(
                    // Coordinateur assigné directement à la participation
                    $qb->expr()->eq('pcp.id', ':coordinatorId'),
                    // Coordinateur de la formation (quand pas de coordinateur spécifique sur la participation)
                    $qb->expr()->andX(
                        $qb->expr()->isNull('p.coordinator'),
                        $qb->expr()->eq('cp.id', ':coordinatorId')
                    )
                )
            )
            ->setParameter('coordinatorId', $coordinator->getId());


        return count($qb->getQuery()->getResult());
    }

    public function countByCategoryOfSpeciality(int $year, ?CoordinatorPerson $person): array
    {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->createQueryBuilder('pa')
            ->select(
        's.category,
                count(DISTINCT pa.id) as participation,
                count(DISTINCT p.id) as participant,
                sum(pa.price)/count(DISTINCT p.id) as fm,
                count(DISTINCT pa.id) / count(DISTINCT p.id) as ratio
            ')
            ->innerJoin('pa.participant', 'p')
            ->innerJoin('pa.formation', 'f')
            ->join(
                Speciality::class,
                's',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                's.speciality = p.speciality'
            )
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere('pa.archived = false')
            ->orderBy('count(DISTINCT pa.id)', 'DESC')
            ->addOrderBy('count(DISTINCT p.id)', 'DESC')
        ;
        $resTotauxAllCR = $qb->getQuery()->getScalarResult();

        $qbBackup = clone $qb;
        $qb
            ->addGroupBy("s.category")
        ;
        $resTotauxAllCRByCategory = $qb->getQuery()->getScalarResult();

        $qb->andWhere('p.coordinator = :person')->setParameter('person', $person->getId());
        $resTotauxMyCRByCategory = $qb->getQuery()->getScalarResult();

        $qbBackup->andWhere('p.coordinator = :person')->setParameter('person', $person->getId());
        $resTotauxMyCR = $qbBackup->getQuery()->getScalarResult();

        $res = $qb->getQuery()->getScalarResult();
        $resPS = $res;
        usort($resPS, fn($a, $b) => $b['participant'] <=> $a['participant']);

        return [
            "participations" => new DTORepartitionCRDashboard('participations',
                array_map(function($row) {
                    return new DTORepartitionCRDashboardLine($row['category'], $row['participation']);
                }, $res)
            ),
            "participantsUniques" => new DTORepartitionCRDashboard('participants uniques',
                array_map(function($row) {
                    return new DTORepartitionCRDashboardLine($row['category'], $row['participant']);
                }, $resPS)
            ),
            "ratioEtForfaitMoyen" => new DTORatioEtForfaitMoyenCRDashboard('participants uniques',
                array_map(function($row) {
                    return new DTORatioEtForfaitMoyenCRDashboardLine($row['category'], $row['ratio'], $row['fm']);
                }, $res)
            )
        ];
    }
}
