<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Repository\SpecialityRepository;

#[ORM\Entity(repositoryClass: SpecialityRepository::class)]
#[ORM\Index(name: 'idx_speciality_category', columns: ['category'])]
#[ORM\Index(name: 'idx_speciality_speciality', columns: ['speciality'])]
class Speciality
{
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    public function __construct(
        #[ORM\Column(name: 'speciality', type: Types::STRING)]
        private readonly string $speciality,
        #[ORM\Column(name: 'category', type: Types::STRING)]
        private readonly string $category,
    )
    {}

    public function getSpeciality(): string
    {
        return $this->speciality;
    }

    public function getCategory(): string
    {
        return $this->category;
    }
}