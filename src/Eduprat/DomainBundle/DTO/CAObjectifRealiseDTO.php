<?php

namespace Eduprat\DomainBundle\DTO;

use Eduprat\AdminBundle\Entity\CoordinatorPerson;

class CAObjectifRealiseDTO
{
    public ?float $CARealise = null;
    public ?float $CAObjectif = null;
    public ?float $cumulMargeRealise = null;
    public ?float $cumulMargeObjectif = null;

    public ?float $primeAnnuelleRealise = null;
    public ?float $primeAnnuelleObjectif = null;

    public ?float $tauxPrimeRealise = null;
    public ?float $tauxPrimeObjectif = null;

    public ?float $nextPalierEcart = null;

    public ?int $nbParticipationsToNextPalier = null;

    public bool $isCrIndependant = true;

    public function __construct(CoordinatorPerson $coordinatorPerson, int $year, float $marges, int $participations)
    {
        $this->isCrIndependant = !$coordinatorPerson->crIsSalarie();
        
//        $this->CARealise = $coordinatorPerson->getAllCoordinatorCaSecteurByCoordinator($year);
        $this->CARealise = -1;
        $this->CAObjectif = $coordinatorPerson->getObjetifCAOf($year);

        $this->calculateCumulMargeObjectif($coordinatorPerson, $year);
        $this->calculateCumulMargeRealise($coordinatorPerson, $year, $marges);

        $this->tauxPrimeRealise = $this->getTauxPrime($this->cumulMargeRealise);
        if ($this->cumulMargeObjectif) {
            $this->tauxPrimeObjectif = $this->getTauxPrime($this->cumulMargeObjectif);
        }

        $this->primeAnnuelleRealise = $this->getPrime($this->tauxPrimeRealise, $this->cumulMargeRealise);
        if ($this->cumulMargeObjectif) {
            $this->primeAnnuelleObjectif = $this->getPrime($this->tauxPrimeObjectif, $this->cumulMargeObjectif);
        }

        $this->nextPalierEcart = $this->calculateNextPalierEcart();
        $this->calculateNbParticipationsToNextPalier($participations);
    }

    public function getCAPercentObjectif(): ?float
    {
        if (null === $this->CAObjectif) {
            return null;
        }
        return min(100, $this->CARealise / $this->CAObjectif * 100);
    }

    public function getCumulMargePercentObjectif(): ?float
    {
        if (null === $this->cumulMargeObjectif) {
            return null;
        }
        return min(100, $this->cumulMargeRealise / $this->cumulMargeObjectif * 100);
    }

    public function getPrimeAnnuellePercentObjectif(): ?float
    {
        if (null === $this->primeAnnuelleObjectif) {
            return null;
        }
        return min(100, $this->primeAnnuelleRealise / $this->primeAnnuelleObjectif * 100);
    }

    public function getTauxPrimePercentObjectif(): ?float
    {
        if (null === $this->tauxPrimeRealise) {
            return null;
        }
        return min(100, $this->tauxPrimeRealise / $this->tauxPrimeObjectif * 100);
    }

    private function calculateCumulMargeObjectif(CoordinatorPerson $coordinatorPerson, $year): void
    {
        if (!$this->CAObjectif) {
            return;
        }
        $this->cumulMargeObjectif = $this->CAObjectif * $coordinatorPerson->getTauxCumulMargeOf($year) / 100;
    }

    private function calculateCumulMargeRealise (CoordinatorPerson $coordinatorPerson, $year, $marges): void
    {
        $this->cumulMargeRealise = !$coordinatorPerson->crIsSalarie() ? $marges : $this->CARealise * ($coordinatorPerson->getTauxCumulMargeOf($year) / 100);
    }

    private function calculateNbParticipationsToNextPalier (int $participations): void {
        $forfaitMoyenParticipation = $this->CARealise && $participations ? $this->CARealise / $participations : 0;
        $this->nbParticipationsToNextPalier = $this->nextPalierEcart && $forfaitMoyenParticipation ? ceil($this->nextPalierEcart / $forfaitMoyenParticipation) : null;
    }

    private function getTauxPrime($marge): float
    {
        $paliers = $this->getPaliers();

        // Taux de base pour les montants inférieurs au premier palier
        $tauxBase = 4;

        foreach ($paliers as $seuil => $info) {
            if ($marge < $seuil) {
                return $tauxBase;
            }
            $tauxBase = $info['taux'];
        }

        // Taux pour les montants supérieurs au dernier palier
        return 10;
    }

    private function getPrime(float $tauxPrimeRealise, float $cumulMargeRealise): float
    {
        return $cumulMargeRealise * $tauxPrimeRealise / 100;
    }

    /**
     * Retourne le montant qu'il manque pour atteindre le palier suivant
     */
    public function calculateNextPalierEcart(): ?float
    {
        if ($this->CARealise === null || $this->CARealise == 0 || $this->cumulMargeRealise === null || $this->cumulMargeRealise == 0) {
            return null;
        }
        
        $paliers = $this->getPaliers();
        $seuilsPaliers = array_keys($paliers);

        // On ne veut pas prendre en compte le premier palier (on est forcément au moins dans le premier parlier)
        if ($this->cumulMargeRealise < $seuilsPaliers[0]) {
                return ($seuilsPaliers[1] - $this->cumulMargeRealise) / ($this->cumulMargeRealise / $this->CARealise);
        }

        foreach ($paliers as $seuil => $info) {
            if ($this->cumulMargeRealise < $seuil) {
                return ($seuil - $this->cumulMargeRealise) / ($this->cumulMargeRealise / $this->CARealise);
            }
        }

        // Si on est déjà au palier maximum
        return null;
    }

    /**
     * Retourne la liste des paliers restants (incluant le palier en cours) avec leurs tranches et taux
     */
    public function getPaliersRestants(): array
    {
        if ($this->cumulMargeRealise === null) {
            return [];
        }

        $paliers = $this->getPaliers();
        $paliersRestants = [];

        // Inclure le palier actuel s'il existe
        $palierActuel = $this->getPalierActuel();
        if ($palierActuel !== null) {
            $paliersRestants[$palierActuel] = [
                "tranche" => $paliers[$palierActuel]['tranche'],
                "taux" => $paliers[$palierActuel]['taux'],
                "value" => $palierActuel
            ];
        }

        // Inclure tous les paliers suivants
        foreach ($paliers as $seuil => $info) {
            if ($this->cumulMargeRealise < $seuil) {
                $paliersRestants[$seuil] = [
                    "tranche" => $info['tranche'],
                    "taux" => $info['taux'],
                    "value" => $seuil
                ];
            }
        }

        return $paliersRestants;
    }

    /**
     * Retourne le seuil du palier actuel (celui dans lequel on se trouve)
     * Retourne null si on est en dessous du premier palier
     */
    private function getPalierActuel(): ?int
    {
        if ($this->cumulMargeRealise === null) {
            return null;
        }

        $paliers = $this->getPaliers();
        $palierActuel = null;

        foreach ($paliers as $seuil => $info) {
            if ($this->cumulMargeRealise >= $seuil) {
                $palierActuel = $seuil;
            } else {
                break;
            }
        }

        return $palierActuel;
    }

    private function getPaliers(): array
    {
        if ($this->isCrIndependant) {
            return $this->getPaliersCrIndependant();
        }

        return [
            0 => ['taux' => 4, 'tranche' => '0 -<br>159k€'],
            160000 => ['taux' => 4.5, 'tranche' => '160 -<br>179k€'],
            180000 => ['taux' => 5, 'tranche' => '180 -<br>199k€'],
            200000 => ['taux' => 5.5, 'tranche' => '200 -<br>219k€'],
            220000 => ['taux' => 6, 'tranche' => '220 -<br>239k€'],
            240000 => ['taux' => 6.5, 'tranche' => '240 -<br>259k€'],
            260000 => ['taux' => 7, 'tranche' => '260 -<br>279k€'],
            280000 => ['taux' => 8, 'tranche' => '280 -<br>319k€'],
            320000 => ['taux' => 8.5, 'tranche' => '320 -<br>339k€'],
            340000 => ['taux' => 9, 'tranche' => '340 -<br>359k€'],
            360000 => ['taux' => 9.5, 'tranche' => '360 -<br>379k€'],
            380000 => ['taux' => 10, 'tranche' => '380k€<br>et plus'],
        ];
    }


    private function getPaliersCrIndependant(): array
    {
        return [
            0 => ['taux' => 34, 'tranche' => '0 -<br>89k€'],
            90000 => ['taux' => 38, 'tranche' => '90 -<br>139k€'],
            140000 => ['taux' => 42, 'tranche' => '140 -<br>189k€'],
            190000 => ['taux' => 46, 'tranche' => '190 -<br>239k€'],
            240000 => ['taux' => 50, 'tranche' => '240k€<br> et plus'],
        ];
    }



}