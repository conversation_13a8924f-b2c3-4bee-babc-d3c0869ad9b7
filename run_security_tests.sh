#!/bin/bash

# Script pour exécuter tous les tests de sécurité
echo "🔐 Exécution des tests de sécurité pour Eduprat"
echo "=============================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les résultats
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2 - SUCCÈS${NC}"
    else
        echo -e "${RED}❌ $2 - ÉCHEC${NC}"
    fi
}

# Vérifier que PHPUnit est disponible
if ! command -v ./vendor/bin/phpunit &> /dev/null; then
    echo -e "${RED}❌ PHPUnit n'est pas installé. Exécutez 'composer install' d'abord.${NC}"
    exit 1
fi

# Vérifier que l'environnement de test est configuré
if [ ! -f ".env.test" ]; then
    echo -e "${YELLOW}⚠️  Fichier .env.test non trouvé. Assurez-vous que l'environnement de test est configuré.${NC}"
fi

echo -e "${BLUE}📋 Préparation de la base de données de test...${NC}"
php bin/console doctrine:database:drop --force --env=test --quiet 2>/dev/null || true
php bin/console doctrine:database:create --env=test --quiet
php bin/console doctrine:schema:create --env=test --quiet

echo -e "${BLUE}🧪 Exécution des tests de contrôle d'accès...${NC}"
echo ""

# Test 1: Tests de contrôle d'accès général
echo -e "${YELLOW}Test 1: Contrôle d'accès général${NC}"
./vendor/bin/phpunit tests/functional/Eduprat/AdminBundle/Security/AccessControlTest.php --verbose
print_result $? "Contrôle d'accès général"
echo ""

# Test 2: Tests d'accès non autorisé
echo -e "${YELLOW}Test 2: Accès non autorisé${NC}"
./vendor/bin/phpunit tests/functional/Eduprat/AdminBundle/Security/UnauthorizedAccessTest.php --verbose
print_result $? "Tests d'accès non autorisé"
echo ""

# Test 3: Tests des annotations de sécurité
echo -e "${YELLOW}Test 3: Annotations de sécurité${NC}"
./vendor/bin/phpunit tests/functional/Eduprat/AdminBundle/Security/SecurityAnnotationTest.php --verbose
print_result $? "Annotations de sécurité"
echo ""

# Exécuter tous les tests de sécurité ensemble
echo -e "${YELLOW}Test complet: Tous les tests de sécurité${NC}"
./vendor/bin/phpunit tests/functional/Eduprat/AdminBundle/Security/ --verbose
OVERALL_RESULT=$?
print_result $OVERALL_RESULT "Tests de sécurité complets"

echo ""
echo "=============================================="
if [ $OVERALL_RESULT -eq 0 ]; then
    echo -e "${GREEN}🎉 Tous les tests de sécurité ont réussi !${NC}"
    echo -e "${GREEN}✅ Les contrôles d'accès fonctionnent correctement.${NC}"
else
    echo -e "${RED}⚠️  Certains tests de sécurité ont échoué.${NC}"
    echo -e "${RED}🔍 Vérifiez les logs ci-dessus pour plus de détails.${NC}"
fi

echo ""
echo -e "${BLUE}📊 Résumé des tests:${NC}"
echo "- Tests de contrôle d'accès général"
echo "- Tests d'accès non autorisé par rôle"  
echo "- Tests des annotations de sécurité"
echo "- Tests de hiérarchie des rôles"
echo "- Tests des méthodes HTTP protégées"

echo ""
echo -e "${BLUE}💡 Pour des tests plus spécifiques:${NC}"
echo "- Testez avec des utilisateurs réels: ./vendor/bin/phpunit --filter testSpecificUser"
echo "- Testez une route spécifique: ./vendor/bin/phpunit --filter testSpecificRoute"
echo "- Mode debug: ./vendor/bin/phpunit --debug"

exit $OVERALL_RESULT
