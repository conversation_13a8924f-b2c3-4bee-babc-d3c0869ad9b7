<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250428131644 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE lead_history (id INT AUTO_INCREMENT NOT NULL, participant INT NOT NULL, advisor INT DEFAULT NULL, leadType VARCHAR(255) DEFAULT NULL, partenariat VARCHAR(255) DEFAULT NULL, leadStatus VARCHAR(255) DEFAULT NULL, leadCreationDate DATE DEFAULT NULL, leadContactDate VARCHAR(255) DEFAULT NULL, leadComment LONGTEXT DEFAULT NULL, gpmMemberNumber VARCHAR(255) DEFAULT NULL, leadState VARCHAR(255) DEFAULT NULL, leadCommentEduprat VARCHAR(255) DEFAULT NULL, leadReferent INT DEFAULT NULL, INDEX IDX_7E742C25D79F6B11 (participant), INDEX IDX_7E742C252A6A7329 (leadReferent), INDEX IDX_7E742C2519ADC9F4 (advisor), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE lead_history ADD CONSTRAINT FK_7E742C25D79F6B11 FOREIGN KEY (participant) REFERENCES participant (id)');
        $this->addSql('ALTER TABLE lead_history ADD CONSTRAINT FK_7E742C252A6A7329 FOREIGN KEY (leadReferent) REFERENCES person (id)');
        $this->addSql('ALTER TABLE lead_history ADD CONSTRAINT FK_7E742C2519ADC9F4 FOREIGN KEY (advisor) REFERENCES person (id)');
        $this->addSql('ALTER TABLE participant ADD birthDate DATETIME DEFAULT NULL, ADD codeApporteur VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE person ADD code_apporteur VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE lead_history DROP FOREIGN KEY FK_7E742C25D79F6B11');
        $this->addSql('ALTER TABLE lead_history DROP FOREIGN KEY FK_7E742C252A6A7329');
        $this->addSql('ALTER TABLE lead_history DROP FOREIGN KEY FK_7E742C2519ADC9F4');
        $this->addSql('DROP TABLE lead_history');
        $this->addSql('ALTER TABLE person DROP code_apporteur');
        $this->addSql('ALTER TABLE participant DROP birthDate, DROP codeApporteur');
    }
}
