<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250113140448 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE mailer_history CHANGE createdAt createdAt DATETIME DEFAULT NULL, CHANGE subject subject LONGTEXT DEFAULT NULL, CHANGE type type VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE mailer_history CHANGE createdAt createdAt DATETIME NOT NULL, CHANGE subject subject LONGTEXT NOT NULL, CHANGE type type VARCHAR(255) NOT NULL');
    }
}
