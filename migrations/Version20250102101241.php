<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Psr\Log\LoggerInterface;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250102101241 extends AbstractMigration
{
    private array $table_columns;

    public function __construct(Connection $connection, LoggerInterface $logger)
    {
        parent::__construct($connection, $logger);
        // table -> colonne => nullable
        $this->table_columns = [
            'module' => [
                'routeParams' => true,
            ],
            'participant_date_download' => [
                'topos' => true,
                'toposDownloadedAt' => true,
            ],
            'person' => [
                'roles' => false,
                'ipList' => false,
            ],
            'programme' => [
                'categories' => true,
                'specialities' => true,
                'exercisesMode' => true,
            ],
            'selection_formation' => [
                'exclusions' => false,
            ]
        ];
    }

    public function getDescription(): string
    {
        return 'migration des champs de array en JSON';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE formation DROP reference, DROP exerciseMode, DROP image, DROP progression, DROP nationalOrientation, DROP durationPresentielle, DROP durationNotPresentielle, DROP billingProgression, DROP fileComplement, DROP exercisesMode, DROP categories, DROP specialities, DROP autocompleteReference, DROP objectives, DROP durationEtape3, DROP duration');
        $this->addSql('ALTER TABLE formation CHANGE financeSousModeFactures financeSousModeFactures JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE participant CHANGE gdprAgreementHistory gdprAgreementHistory JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');
        $this->addSql('ALTER TABLE selection_formation CHANGE searchFields searchFields JSON DEFAULT NULL COMMENT \'(DC2Type:json)\'');


        foreach ($this->table_columns as $tableName => $columns) {
            $columnNames = array_keys($columns);
            $selectColumns = implode(', ', $columnNames);
            $nullableColumns = $columns;

            // Récupérer toutes les colonnes nécessaires en une seule requête
            $data = $this->connection->fetchAllAssociative(
                "SELECT id, $selectColumns FROM $tableName"
            );

            $updates = [];

            foreach ($data as $row) {
                $id = $row['id'];

                foreach ($nullableColumns as $columnName => $nullable) {
                    $sanitizedValue = $this->sanitizeData(
                        $row[$columnName],
                        $nullable,
                        $id,
                        $tableName,
                        $columnName
                    );

                    if ($sanitizedValue !== null) {
                        $updates[$id][$columnName] = json_encode($sanitizedValue);
                    }
                }
            }

            // Exécuter toutes les mises à jour regroupées
            $this->executeBatchUpdate($tableName, $updates);

            // Mettre à jour les colonnes pour le type JSON
            foreach ($nullableColumns as $columnName => $nullable) {
                $nullableString = $nullable ? "DEFAULT NULL" : "NOT NULL";
                $this->addSql(
                    "ALTER TABLE $tableName CHANGE $columnName $columnName JSON {$nullableString} COMMENT '(DC2Type:json)'"
                );
            }
        }
    }

    private function sanitizeData($value, bool $nullable, int $id, string $tableName, string $columnName)
    {
        if ($value === null || $value === '') {
            return $nullable ? null : unserialize("a:0:{}");
        }

        if ($value === "N;") {
            return unserialize("a:0:{}");
        }

        try {
            return unserialize($value);
        } catch (\Exception $e) {
            $this->logError($id, $tableName, $columnName, $value, $e);
            throw $e;
        }
    }

    private function logError(int $id, string $tableName, string $columnName, $value, \Exception $exception): void
    {
        dump([
            'id' => $id,
            'table' => $tableName,
            'column' => $columnName,
            'value' => $value,
            'error' => $exception->getMessage(),
        ]);
    }

    private function executeBatchUpdate(string $tableName, array $updates): void
    {
        foreach ($updates as $id => $columns) {
            $setClauses = [];
            $parameters = ['id' => $id];

            foreach ($columns as $column => $value) {
                $setClauses[] = "$column = :$column";
                $parameters[$column] = $value;
            }

            $setQuery = implode(', ', $setClauses);
            $this->addSql(
                "UPDATE $tableName SET $setQuery WHERE id = :id",
                $parameters
            );
        }
    }

    public function down(Schema $schema): void
    {
        foreach ($this->table_columns as $tableName => $columns) {
            foreach ($columns as $columnName => $nullable) {
                $nullableString = $nullable ? "DEFAULT NULL" : "NOT NULL";
                $this->addSql("ALTER TABLE $tableName CHANGE $columnName $columnName LONGTEXT {$nullableString} COMMENT '(DC2Type:array)'");

                // Récupération des données existantes au format JSON
                $data = $this->connection->fetchAllAssociative("SELECT id, $columnName FROM $tableName");

                foreach ($data as $row) {
                    $id = $row['id'];
                    $jsonData = $row[$columnName];

                    if (!empty($jsonData)) {
                        $arrayData = json_decode($jsonData, true); // Convertir le JSON en tableau
                        if (is_array($arrayData) && empty($arrayData)) {
                            $arrayData = null;
                        }
                        $serializedData = serialize($arrayData); // Sérialiser le tableau
                        $this->addSql(
                            "UPDATE $tableName SET $columnName = :data WHERE id = :id",
                            ['data' => $serializedData, 'id' => $id]
                        );
                    }
                }
            }
        }
    }
}
