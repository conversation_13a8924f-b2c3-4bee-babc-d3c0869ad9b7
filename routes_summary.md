# LISTE COMPLÈTE DES ROUTES ET RÔLES D'ACCÈS - EDUPRAT

## HIÉRARCHIE DES RÔLES

```
ROLE_USER (base)
├── ROLE_FORMER
├── ROLE_COORDINATOR_LBI
│   └── ROLE_COORDINATOR
│       ├── ROLE_SUPERVISOR
│       │   └── ROLE_SUPERVISOR_FRANCE
│       └── ROLE_FORMER_PHARMACIE
└── ROLE_WEBMASTER (hérite de FORMER, COORDINATOR, SUPERVISOR, SUPERVISOR_FRANCE)
    └── ROLE_WEBMASTER_COMPTA
        └── ROLE_SUPER_ADMIN (+ ROLE_ALLOWED_TO_SWITCH)
```

## ZONES D'ACCÈS PRINCIPALES

### 1. ZONE PUBLIQUE (PUBLIC_ACCESS)
- `/pdf/*` - Génération de PDF
- `/api/*` - API publique
- `/login` - Pages de connexion
- `/sso` - Single Sign-On
- `/topo/download` - Téléchargement de documents
- `/gotenberg/*` - Service de génération PDF

### 2. ZON<PERSON> ADMIN (/gestion)
**Accès requis :** ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE

### 3. ZONE CRM (/crm)
**Accès requis :** ROLE_FORMER, ROLE_COORDINATOR, ROLE_COORDINATOR_LBI, ROLE_SUPERVISOR, ROLE_SUPERVISOR_FRANCE

### 4. ZONE AUDIT (/)
**Accès requis :** IS_AUTHENTICATED_FULLY (participants aux formations)

## ROUTES PAR RÔLE

### ROLE_SUPER_ADMIN
- Accès à toutes les fonctionnalités
- Peut changer d'utilisateur (ROLE_ALLOWED_TO_SWITCH)

### ROLE_WEBMASTER_COMPTA
- Toutes les fonctionnalités WEBMASTER
- `admin_formation_account` - Comptabilité des formations

### ROLE_WEBMASTER
- **Gestion des audits :** Création, édition, suppression, archivage
- **Gestion des e-learnings :** CRUD complet
- **Configuration système :** Emails automatiques, paramètres
- **Gestion des modes de financement :** CRUD complet
- **Gestion des indemnisations :** CRUD complet
- **Gestion des enquêtes :** CRUD complet
- **Gestion des FAQ :** CRUD complet
- **Gestion des tags :** CRUD complet
- **Exports CSV :** Génération de rapports
- **Formations :** Création, édition, suppression, facturation
- **Évaluations :** Accès aux évaluations globales utilisateurs

### ROLE_SUPERVISOR / ROLE_SUPERVISOR_FRANCE
- Hérite de ROLE_COORDINATOR
- **Exports CSV avancés :** Génération de rapports étendus

### ROLE_COORDINATOR
- **Formations :** Consultation, duplication, envoi d'emails, exports
- **Participants :** Édition, gestion des emails, exports
- **Participations :** Édition, gestion des audits/enquêtes
- **Plaquettes :** Recherche et gestion des favoris
- **Fichiers :** Téléchargement de documents (émargement, convention, etc.)
- **Évaluations :** Accès aux évaluations par coordinateur/formateur
- **Comptabilité :** Suivi des formations (selon coordinateur)

### ROLE_COORDINATOR_LBI
- Hérite de ROLE_USER
- **Formations :** Consultation, gestion des participants
- **Participants :** Création, suppression, consultation
- **Comptabilité CRM :** Suivi des attestations, modules manquants
- **Leads :** Suivi des prospects
- **Évaluations :** Accès aux évaluations globales par rôle
- **Facturation :** Gestion des états de facture

### ROLE_FORMER
- Hérite de ROLE_USER
- **Formations :** Consultation de base
- **Monitoring :** Suivi de ses propres formations

### PARTICIPANTS (IS_AUTHENTICATED_FULLY)
- **Formations :** Accès aux modules de formation
- **Audits :** Réponse aux questionnaires d'audit
- **Enquêtes :** Réponse aux enquêtes
- **E-learning :** Accès aux cours en ligne
- **Évaluations :** Évaluation des formations et formateurs
- **Profil :** Gestion de son profil personnel

## ROUTES SANS RESTRICTION DE RÔLE SPÉCIFIQUE
- `admin_index` - Page d'accueil admin (redirection selon le rôle)
- `admin_programme_index` - Liste des programmes
- `admin_programme_dosearch` - Recherche de programmes
- Diverses routes de téléchargement et d'affichage

## SÉCURITÉ ADDITIONNELLE
- **2FA :** Authentification à deux facteurs activée
- **Throttling :** Limitation des tentatives de connexion (10 max/heure)
- **CSRF :** Protection contre les attaques CSRF
- **Context partagé :** Authentification partagée entre zones admin/CRM
